<?php
require_once 'classes/Branch.php';

echo "<h2>Quick Branch Setup for Login Testing</h2>";

try {
    $branch = new Branch();
    
    // Check if branches exist
    $all_branches = $branch->getAllBranches();
    
    if (empty($all_branches)) {
        echo "<p style='color: red;'>❌ No branches found in database.</p>";
        echo "<p><strong>Solution:</strong> Please run <a href='setup_database.php'>setup_database.php</a> first to create the database and branches.</p>";
        
        echo "<h3>Quick Database Setup:</h3>";
        echo "<ol>";
        echo "<li>Click <a href='setup_database.php' target='_blank'>setup_database.php</a></li>";
        echo "<li>Wait for the setup to complete</li>";
        echo "<li>Then test the branch login URLs below</li>";
        echo "</ol>";
        
    } else {
        echo "<p style='color: green;'>✅ Found " . count($all_branches) . " branches in database.</p>";
        
        echo "<h3>Branch Login URLs (Ready to Test):</h3>";
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        
        foreach ($all_branches as $branch_data) {
            echo "<p><strong>{$branch_data['name']} Branch:</strong></p>";
            echo "<p><a href='branch_login.php?branch={$branch_data['id']}' target='_blank' style='background: #22c55e; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;'>branch_login.php?branch={$branch_data['id']}</a></p>";
        }
        
        echo "</div>";
        
        echo "<h3>Expected Login Credentials:</h3>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>For each branch, try these credentials:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Manager:</strong> [branch]_manager / password123</li>";
        echo "<li><strong>Staff:</strong> [branch]_staff / password123</li>";
        echo "</ul>";
        echo "<p><strong>Examples:</strong></p>";
        echo "<ul>";
        echo "<li>kasaragod_manager / password123</li>";
        echo "<li>kasaragod_staff / password123</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>Test Results:</h3>";
        echo "<p>When you click the branch login URLs above, you should see:</p>";
        echo "<ol>";
        echo "<li>A beautiful login form with the branch name</li>";
        echo "<li>Username and password fields</li>";
        echo "<li>Branch information displayed</li>";
        echo "<li>Login button saying 'Sign In to [Branch Name]'</li>";
        echo "</ol>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Solution:</strong> Check your database connection and run <a href='setup_database.php'>setup_database.php</a></p>";
}

echo "<hr>";
echo "<h3>Troubleshooting:</h3>";
echo "<ul>";
echo "<li><strong>If you see 'Branch not found':</strong> Run <a href='setup_database.php'>setup_database.php</a></li>";
echo "<li><strong>If you see database errors:</strong> Check database configuration in config/database.php</li>";
echo "<li><strong>If login doesn't work:</strong> Run <a href='setup_branch_users.php'>setup_branch_users.php</a> to create users</li>";
echo "<li><strong>For debugging:</strong> Visit <a href='branch_login_debug.php'>branch_login_debug.php</a></li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Complete Setup Steps:</h3>";
echo "<ol>";
echo "<li><a href='setup_database.php'>Setup Database</a> - Creates tables and sample data</li>";
echo "<li><a href='setup_branch_users.php'>Create Branch Users</a> - Creates login accounts</li>";
echo "<li><a href='branch_login.php?branch=3'>Test Branch 3 Login</a> - Test the specific URL you mentioned</li>";
echo "</ol>";
?>
