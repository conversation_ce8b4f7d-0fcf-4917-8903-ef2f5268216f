<?php
require_once 'classes/User.php';
require_once 'classes/Branch.php';

echo "<h2>Branch User Setup</h2>";
echo "<p>Setting up users for each branch...</p>";

try {
    $user = new User();
    $branch = new Branch();
    
    // Get all branches
    $branches = $branch->getAllBranches();
    
    if (empty($branches)) {
        echo "<p style='color: red;'>No branches found. Please run setup_database.php first.</p>";
        exit;
    }
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Branch Users Setup</h4>";
    
    $created_users = [];
    
    foreach ($branches as $branch_data) {
        $branch_id = $branch_data['id'];
        $branch_name = $branch_data['name'];
        $clean_branch_name = strtolower(str_replace(' ', '', $branch_name));
        
        // Create branch manager user
        $manager_username = $clean_branch_name . '_manager';
        $manager_exists = $user->getUserByUsername($manager_username);
        
        if (!$manager_exists) {
            $result = $user->createUser(
                $manager_username,
                "manager@{$clean_branch_name}.com",
                'password123',
                'branch_manager',
                $branch_id,
                ucfirst($branch_name) . ' Manager',
                'User',
                '9876543210'
            );
            
            if ($result) {
                echo "<p style='color: green;'>✓ Created manager for {$branch_name}: {$manager_username}</p>";
                $created_users[] = [
                    'branch' => $branch_name,
                    'username' => $manager_username,
                    'password' => 'password123',
                    'role' => 'branch_manager'
                ];
            } else {
                echo "<p style='color: red;'>✗ Failed to create manager for {$branch_name}</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Manager already exists for {$branch_name}: {$manager_username}</p>";
        }
        
        // Create branch staff user
        $staff_username = $clean_branch_name . '_staff';
        $staff_exists = $user->getUserByUsername($staff_username);
        
        if (!$staff_exists) {
            $result = $user->createUser(
                $staff_username,
                "staff@{$clean_branch_name}.com",
                'password123',
                'branch_staff',
                $branch_id,
                ucfirst($branch_name) . ' Staff',
                'User',
                '9876543211'
            );
            
            if ($result) {
                echo "<p style='color: green;'>✓ Created staff for {$branch_name}: {$staff_username}</p>";
                $created_users[] = [
                    'branch' => $branch_name,
                    'username' => $staff_username,
                    'password' => 'password123',
                    'role' => 'branch_staff'
                ];
            } else {
                echo "<p style='color: red;'>✗ Failed to create staff for {$branch_name}</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Staff already exists for {$branch_name}: {$staff_username}</p>";
        }
    }
    
    echo "</div>";
    
    // Show created users summary
    if (!empty($created_users)) {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: green; margin-top: 0;'>🎉 Branch Users Created Successfully!</h4>";
        echo "<p>You can now login to each branch using these credentials:</p>";
        
        echo "<div class='row'>";
        $current_branch = '';
        foreach ($created_users as $user_data) {
            if ($current_branch !== $user_data['branch']) {
                if ($current_branch !== '') echo "</div></div>";
                echo "<div class='col-md-6 mb-3'>";
                echo "<div class='card'>";
                echo "<div class='card-header'><strong>{$user_data['branch']} Branch</strong></div>";
                echo "<div class='card-body'>";
                $current_branch = $user_data['branch'];
            }
            
            echo "<div class='mb-2'>";
            echo "<strong>" . ucfirst(str_replace('_', ' ', $user_data['role'])) . ":</strong><br>";
            echo "<small>Username: <code>{$user_data['username']}</code></small><br>";
            echo "<small>Password: <code>{$user_data['password']}</code></small>";
            echo "</div>";
        }
        if ($current_branch !== '') echo "</div></div></div>";
        echo "</div>";
        
        echo "<div class='mt-4'>";
        echo "<h5>How to Login:</h5>";
        echo "<ol>";
        echo "<li>Go to <a href='branch_login.php' target='_blank'>Branch Login Page</a></li>";
        echo "<li>Select your branch</li>";
        echo "<li>Use the credentials above</li>";
        echo "<li>You'll be redirected to your branch dashboard</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    // Show all users in system
    echo "<hr>";
    echo "<h3>All Users in System:</h3>";
    $allUsers = $user->getAllUsers();
    
    if (empty($allUsers)) {
        echo "<p>No users found in database.</p>";
    } else {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped table-bordered'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Branch</th><th>Status</th><th>Actions</th></tr>";
        echo "</thead>";
        echo "<tbody>";
        foreach ($allUsers as $u) {
            echo "<tr>";
            echo "<td>" . $u['id'] . "</td>";
            echo "<td><strong>" . $u['username'] . "</strong></td>";
            echo "<td>" . $u['email'] . "</td>";
            echo "<td><span class='badge bg-" . ($u['role'] === 'admin' ? 'danger' : ($u['role'] === 'branch_manager' ? 'warning' : 'info')) . "'>" . ucfirst(str_replace('_', ' ', $u['role'])) . "</span></td>";
            echo "<td>" . ($u['branch_name'] ?? 'None') . "</td>";
            echo "<td><span class='badge bg-" . (($u['status'] ?? 'active') === 'active' ? 'success' : 'secondary') . "'>" . ucfirst($u['status'] ?? 'active') . "</span></td>";
            echo "<td>";
            echo "<a href='branch_login.php?branch=" . ($u['branch_id'] ?? '') . "' class='btn btn-sm btn-primary' title='Login as this user'>Login</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>The database might not be set up yet. Please run <a href='setup_database.php'>setup_database.php</a> first.</p>";
}

echo "<hr>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='branch_login.php' style='background: #22c55e; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Branch Login</a>";
echo "<a href='login.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Admin Login</a>";
echo "<a href='index.php' style='background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Main Dashboard</a>";
echo "</div>";
?>
