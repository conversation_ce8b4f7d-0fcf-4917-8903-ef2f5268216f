<?php
require_once 'classes/User.php';
require_once 'classes/Branch.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Complete Branch Setup</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body style='background: #f8f9fa;'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-lg-10'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h2 class='mb-0'><i class='fas fa-building me-2'></i>Complete Branch System Setup</h2>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $user = new User();
    $branch = new Branch();
    
    // Step 1: Check if database is set up
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>Step 1: Database Check</h5>";
    
    $branches = $branch->getAllBranches();
    if (empty($branches)) {
        echo "<p class='text-danger'>❌ No branches found. Please run <a href='setup_database.php'>setup_database.php</a> first.</p>";
        echo "</div>";
        echo "</div></div></div></div></div>";
        echo "</body></html>";
        exit;
    }
    
    echo "<p class='text-success'>✅ Database is set up with " . count($branches) . " branches.</p>";
    echo "</div>";
    
    // Step 2: Create admin user
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-user-shield me-2'></i>Step 2: Admin User Setup</h5>";
    
    $adminUser = $user->getUserByUsername('admin');
    if (!$adminUser) {
        $result = $user->createUser(
            'admin',
            '<EMAIL>',
            'admin123',
            'admin',
            null,
            'Admin',
            'User',
            '9999999999'
        );
        
        if ($result) {
            echo "<p class='text-success'>✅ Admin user created successfully!</p>";
        } else {
            echo "<p class='text-danger'>❌ Failed to create admin user</p>";
        }
    } else {
        echo "<p class='text-info'>ℹ️ Admin user already exists</p>";
    }
    echo "</div>";
    
    // Step 3: Create branch users
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-users me-2'></i>Step 3: Branch Users Setup</h5>";
    
    $created_users = [];
    
    foreach ($branches as $branch_data) {
        $branch_id = $branch_data['id'];
        $branch_name = $branch_data['name'];
        $clean_branch_name = strtolower(str_replace(' ', '', $branch_name));
        
        // Create branch manager
        $manager_username = $clean_branch_name . '_manager';
        $manager_exists = $user->getUserByUsername($manager_username);
        
        if (!$manager_exists) {
            $result = $user->createUser(
                $manager_username,
                "manager@{$clean_branch_name}.com",
                'password123',
                'branch_manager',
                $branch_id,
                ucfirst($branch_name) . ' Manager',
                'User',
                '9876543210'
            );
            
            if ($result) {
                $created_users[] = [
                    'branch' => $branch_name,
                    'username' => $manager_username,
                    'password' => 'password123',
                    'role' => 'branch_manager'
                ];
            }
        }
        
        // Create branch staff
        $staff_username = $clean_branch_name . '_staff';
        $staff_exists = $user->getUserByUsername($staff_username);
        
        if (!$staff_exists) {
            $result = $user->createUser(
                $staff_username,
                "staff@{$clean_branch_name}.com",
                'password123',
                'branch_staff',
                $branch_id,
                ucfirst($branch_name) . ' Staff',
                'User',
                '9876543211'
            );
            
            if ($result) {
                $created_users[] = [
                    'branch' => $branch_name,
                    'username' => $staff_username,
                    'password' => 'password123',
                    'role' => 'branch_staff'
                ];
            }
        }
    }
    
    echo "<p class='text-success'>✅ Branch users setup completed!</p>";
    echo "</div>";
    
    // Step 4: Show login credentials
    echo "<div class='alert alert-primary'>";
    echo "<h5><i class='fas fa-key me-2'></i>Step 4: Login Credentials</h5>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h6 class='text-danger'>Admin Access:</h6>";
    echo "<div class='bg-light p-3 rounded mb-3'>";
    echo "<strong>Username:</strong> admin<br>";
    echo "<strong>Password:</strong> admin123<br>";
    echo "<strong>Access:</strong> Full system access";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<h6 class='text-success'>Branch Access:</h6>";
    echo "<div class='bg-light p-3 rounded mb-3'>";
    echo "<strong>Format:</strong> [branch]_manager / [branch]_staff<br>";
    echo "<strong>Password:</strong> password123<br>";
    echo "<strong>Example:</strong> uppala_manager, kochi_staff";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Step 5: Show branch-specific credentials
    if (!empty($created_users)) {
        echo "<div class='alert alert-info'>";
        echo "<h5><i class='fas fa-list me-2'></i>Step 5: Branch-Specific Credentials</h5>";
        
        $branches_with_users = [];
        foreach ($created_users as $user_data) {
            $branches_with_users[$user_data['branch']][] = $user_data;
        }
        
        echo "<div class='row'>";
        foreach ($branches_with_users as $branch_name => $users) {
            echo "<div class='col-md-6 mb-3'>";
            echo "<div class='card border-primary'>";
            echo "<div class='card-header bg-primary text-white'>";
            echo "<strong><i class='fas fa-building me-2'></i>{$branch_name} Branch</strong>";
            echo "</div>";
            echo "<div class='card-body'>";
            
            foreach ($users as $user_data) {
                echo "<div class='mb-2 p-2 bg-light rounded'>";
                echo "<strong>" . ucfirst(str_replace('_', ' ', $user_data['role'])) . ":</strong><br>";
                echo "<small class='text-muted'>Username: <code>{$user_data['username']}</code></small><br>";
                echo "<small class='text-muted'>Password: <code>{$user_data['password']}</code></small>";
                echo "</div>";
            }
            
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
    }
    
    // Step 6: Show access instructions
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-rocket me-2'></i>Step 6: How to Access</h5>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h6><i class='fas fa-user-shield me-2'></i>Admin Access:</h6>";
    echo "<ol>";
    echo "<li>Go to <a href='login.php' target='_blank'>Admin Login</a></li>";
    echo "<li>Use admin credentials</li>";
    echo "<li>Access full system management</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<h6><i class='fas fa-building me-2'></i>Branch Access:</h6>";
    echo "<ol>";
    echo "<li>Go to <a href='branch_login.php' target='_blank'>Branch Login</a></li>";
    echo "<li>Select your branch</li>";
    echo "<li>Use branch credentials</li>";
    echo "<li>Access branch-specific dashboard</li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='text-center mt-4'>";
    echo "<div class='btn-group' role='group'>";
    echo "<a href='login.php' class='btn btn-danger'><i class='fas fa-user-shield me-2'></i>Admin Login</a>";
    echo "<a href='branch_login.php' class='btn btn-primary'><i class='fas fa-building me-2'></i>Branch Login</a>";
    echo "<a href='index.php' class='btn btn-success'><i class='fas fa-tachometer-alt me-2'></i>Dashboard</a>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // Show system summary
    echo "<div class='card mt-4'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h5 class='mb-0'><i class='fas fa-info-circle me-2'></i>System Summary</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    $allUsers = $user->getAllUsers();
    $userStats = $user->getUserStats();
    
    echo "<div class='row text-center'>";
    echo "<div class='col-md-3'>";
    echo "<div class='border rounded p-3'>";
    echo "<h3 class='text-primary'>" . count($branches) . "</h3>";
    echo "<small class='text-muted'>Branches</small>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='border rounded p-3'>";
    echo "<h3 class='text-success'>" . count($allUsers) . "</h3>";
    echo "<small class='text-muted'>Total Users</small>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='border rounded p-3'>";
    echo "<h3 class='text-warning'>" . ($userStats['admin_count'] ?? 0) . "</h3>";
    echo "<small class='text-muted'>Admins</small>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='border rounded p-3'>";
    echo "<h3 class='text-info'>" . (($userStats['manager_count'] ?? 0) + ($userStats['staff_count'] ?? 0)) . "</h3>";
    echo "<small class='text-muted'>Branch Users</small>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ Setup Failed</h5>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try running <a href='setup_database.php'>setup_database.php</a> first.</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</body></html>";
?>
