<?php
session_start();

// Check if user is logged in and has proper authentication
if (!isset($_SESSION['user_id']) || !isset($_SESSION['branch_authenticated'])) {
    header('Location: login.php');
    exit;
}

// Check session timeout (8 hours)
if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 28800) {
    session_destroy();
    header('Location: login.php?timeout=1');
    exit;
}

require_once 'classes/Dashboard.php';

$dashboard = new Dashboard();
$summary = $dashboard->getDashboardSummary();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Management System - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand premium-brand" href="index.php">
                <div class="brand-text">
                    <span class="brand-main">KFT Manager</span>
                </div>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php" title="Dashboard">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php" title="Transactions">
                            <i class="fas fa-receipt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="all_branches_dashboard.php" title="All Branches">
                            <i class="fas fa-building"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="branch_login.php" title="Branch Login">
                            <i class="fas fa-sign-in-alt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php" title="Settings">
                            <i class="fas fa-cog"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Alert Container -->
    <div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>

    <!-- Main Content -->
    <div class="main-container" style="margin-top: 85px;">
        <!-- Page Header -->
        <div class="row mb-3">
            <div class="col-12">
            </div>
        </div>

        <!-- Compact Summary Cards -->
        <div class="row mb-3">
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-6 mb-3">
                <div class="stats-card" style="border-left: 4px solid #22c55e;">
                    <div class="stats-icon" style="background: #22c55e; color: white;">
                        <i class="fas fa-arrow-up fa-sm"></i>
                    </div>
                    <div class="stats-value" style="color: #22c55e;">₹<?php echo number_format($dashboard->getTotalIncome(), 2); ?></div>
                    <div class="stats-label">Total Income</div>
                </div>
            </div>

            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-6 mb-3">
                <div class="stats-card" style="border-left: 4px solid #ef4444;">
                    <div class="stats-icon" style="background: #ef4444; color: white;">
                        <i class="fas fa-arrow-down fa-sm"></i>
                    </div>
                    <div class="stats-value" style="color: #ef4444;">₹<?php echo number_format($summary['total_expenses'], 2); ?></div>
                    <div class="stats-label">Total Expenses</div>
                </div>
            </div>

            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-6 mb-3">
                <div class="stats-card" style="border-left: 4px solid <?php echo $dashboard->getNetProfit() >= 0 ? '#22c55e' : '#ef4444'; ?>;">
                    <div class="stats-icon" style="background: <?php echo $dashboard->getNetProfit() >= 0 ? '#22c55e' : '#ef4444'; ?>; color: white;">
                        <i class="fas fa-chart-line fa-sm"></i>
                    </div>
                    <div class="stats-value" style="color: <?php echo $dashboard->getNetProfit() >= 0 ? '#22c55e' : '#ef4444'; ?>;">₹<?php echo number_format($dashboard->getNetProfit(), 2); ?></div>
                    <div class="stats-label">Net Profit</div>
                </div>
            </div>

            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-6 mb-3">
                <div class="stats-card warning">
                    <div class="stats-icon">
                        <i class="fas fa-users fa-sm"></i>
                    </div>
                    <div class="stats-value">
                        <?php 
                        $totalStudents = array_sum(array_column($summary['student_stats'], 'total_students'));
                        echo $totalStudents;
                        ?>
                    </div>
                    <div class="stats-label">Total Students</div>
                </div>
            </div>
        </div>

        <!-- Branch Statistics -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0 fw-bold text-center">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>Branch Performance
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-0 text-center">Branch</th>
                                        <th class="border-0 text-center">Income</th>
                                        <th class="border-0 text-center">Expenses</th>
                                        <th class="border-0 text-center">Net Profit</th>
                                        <th class="border-0 text-center">Students</th>
                                        <th class="border-0 text-center">Enquiries</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $transactionsByBranch = $dashboard->getTransactionsByBranch();
                                    foreach($transactionsByBranch as $branch): 
                                        $netProfit = $branch['total_income'] - $branch['total_expenses'];
                                    ?>
                                    <tr>
                                        <td class="text-center">
                                            <strong class="fw-semibold"><?php echo htmlspecialchars($branch['branch_name']); ?></strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="fw-bold" style="color: #22c55e;">₹<?php echo number_format($branch['total_income'], 2); ?></span>
                                        </td>
                                        <td class="text-center">
                                            <span class="fw-bold" style="color: #ef4444;">₹<?php echo number_format($branch['total_expenses'], 2); ?></span>
                                        </td>
                                        <td class="text-center">
                                            <span class="fw-bold" style="color: <?php echo $netProfit >= 0 ? '#22c55e' : '#ef4444'; ?>;">
                                                ₹<?php echo number_format($netProfit, 2); ?>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <?php 
                                            $studentData = array_filter($summary['student_stats'], function($s) use ($branch) {
                                                return $s['branch_name'] === $branch['branch_name'];
                                            });
                                            $studentCount = !empty($studentData) ? array_values($studentData)[0]['total_students'] : 0;
                                            ?>
                                            <span class="fw-semibold text-success"><?php echo $studentCount; ?></span>
                                        </td>
                                        <td class="text-center">
                                            <?php 
                                            $enquiryData = array_filter($summary['enquiry_stats'], function($e) use ($branch) {
                                                return $e['branch_name'] === $branch['branch_name'];
                                            });
                                            $enquiryCount = !empty($enquiryData) ? array_values($enquiryData)[0]['total_enquiries'] : 0;
                                            ?>
                                            <span class="fw-semibold text-warning"><?php echo $enquiryCount; ?></span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Recent Transactions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 fw-bold">
                                <i class="fas fa-clock me-1 text-primary fa-sm"></i>Recent Transactions
                            </h6>
                            <a href="transactions.php" class="btn btn-sm btn-outline-primary">
                                View All <i class="fas fa-arrow-right ms-1 fa-xs"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-0">Date</th>
                                        <th class="border-0 important-column">Description</th>
                                        <th class="border-0">Type</th>
                                        <th class="border-0">Category</th>
                                        <th class="border-0 important-column">Amount</th>
                                        <th class="border-0">Branch</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($summary['recent_transactions'] as $transaction): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong class="fw-semibold"><?php echo date('M d', strtotime($transaction['date'] ?? date('Y-m-d'))); ?></strong>
                                                <br><small class="text-muted"><?php echo date('Y', strtotime($transaction['date'] ?? date('Y-m-d'))); ?></small>
                                            </div>
                                        </td>
                                        <td class="important-column">
                                            <div>
                                                <strong class="fw-semibold"><?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?></strong>
                                                <br><small class="text-muted">Transaction ID: #<?php echo str_pad($transaction['id'] ?? '0', 4, '0', STR_PAD_LEFT); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="transaction-type <?php echo $transaction['type'] ?? 'unknown'; ?>">
                                                <?php echo ucfirst($transaction['type'] ?? 'Unknown'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($transaction['category'] ?? 'Uncategorized'); ?></span>
                                        </td>
                                        <td class="important-column">
                                            <span class="fw-semibold amount-<?php echo $transaction['type'] === 'income' ? 'income' : 'expense'; ?>">
                                                ₹<?php echo number_format($transaction['amount'] ?? 0, 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle bg-success-100 d-flex align-items-center justify-content-center me-2" style="width: 20px; height: 20px;">
                                                    <i class="fas fa-building text-success-600 fa-xs"></i>
                                                </div>
                                                <span class="fw-semibold"><?php echo htmlspecialchars($transaction['branch_name'] ?? 'Unknown Branch'); ?></span>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light d-lg-none">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branches.php" class="nav-link">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branches</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Floating Action Button -->
    <div class="fab-container">
        <a href="transactions.php?source=dashboard" class="fab-btn" title="Add Transaction">
            <i class="fas fa-plus"></i>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
