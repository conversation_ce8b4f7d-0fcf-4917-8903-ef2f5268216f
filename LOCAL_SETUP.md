# Local Development Setup Guide

This guide will help you set up the Expense Management System for local development using localhost.

## Prerequisites

- **XAMPP/WAMP/LAMP** or any local server environment
- **PHP 7.4+** with PDO MySQL extension
- **MySQL 5.7+** or **MariaDB 10.3+**
- **Web browser** (Chrome, Firefox, Safari, etc.)

## Quick Setup Steps

### 1. Start Your Local Server

**For XAMPP:**
- Start Apache and MySQL services
- Access via `http://localhost`

**For WAMP:**
- Start all services
- Access via `http://localhost`

**For LAMP:**
- Start Apache and MySQL services
- Access via `http://localhost`

### 2. Database Setup

1. Open **phpMyAdmin** (`http://localhost/phpmyadmin`)
2. Import the database file: `setup_local_database.sql`
   - Click "Import" tab
   - Choose file: `setup_local_database.sql`
   - Click "Go"

**OR** run the SQL commands manually:

```sql
-- Create database
CREATE DATABASE expense_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE expense_management;

-- Then run all the CREATE TABLE and INSERT statements from setup_local_database.sql
```

### 3. Place Project Files

1. Copy the entire `new_expns_app` folder to your web server directory:
   - **XAMPP**: `C:\xampp\htdocs\new_expns_app\`
   - **WAMP**: `C:\wamp64\www\new_expns_app\`
   - **LAMP**: `/var/www/html/new_expns_app/`

### 4. Access the Application

Open your web browser and navigate to:
```
http://localhost/new_expns_app
```

## Configuration Details

### Database Configuration
- **Host**: `localhost`
- **Database**: `expense_management`
- **Username**: `root`
- **Password**: (empty)

### Base URL Configuration
- **Base URL**: `http://localhost`
- **App URL**: `http://localhost/new_expns_app`

## Default Login Credentials

- **Username**: `admin`
- **Password**: `admin`

## File Structure

```
new_expns_app/
├── config/
│   ├── database_config.php      # Local database settings
│   ├── database.php             # Database class
│   ├── production.php           # Main configuration
│   └── production_config.php    # Extended configuration
├── assets/
│   ├── css/
│   │   └── style.css           # Premium styling
│   └── js/
│       └── main.js             # Enhanced JavaScript
├── classes/                    # PHP classes
├── api/                        # API endpoints
├── index.php                   # Dashboard
├── login.php                   # Login page
├── transactions.php            # Transactions page
├── branches.php                # Branches page
└── setup_local_database.sql    # Database setup file
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure MySQL is running
   - Check database credentials in `config/database_config.php`
   - Verify database `expense_management` exists

2. **Page Not Found (404)**
   - Check if files are in correct directory
   - Verify Apache is running
   - Check URL path

3. **CSS/JS Not Loading**
   - Check file permissions
   - Verify asset file paths
   - Clear browser cache

4. **Login Not Working**
   - Use default credentials: admin/admin
   - Check session configuration
   - Verify PHP session support

### Database Connection Test

Create a test file `test_db.php` in your project root:

```php
<?php
require_once 'config/database_config.php';

$pdo = testDatabaseConnection();
if ($pdo) {
    echo "✅ Database connection successful!";
} else {
    echo "❌ Database connection failed!";
}
?>
```

## Development Tips

1. **Enable Error Reporting** for development:
   ```php
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

2. **Use Browser Developer Tools** to debug JavaScript and CSS

3. **Check Apache/PHP Error Logs** for server-side issues

4. **Use phpMyAdmin** for database management and debugging

## Features Available

- ✅ Premium modern UI design
- ✅ Responsive mobile-friendly interface
- ✅ Dashboard with statistics
- ✅ Transaction management
- ✅ Branch management
- ✅ Student and enquiry tracking
- ✅ Real-time data updates
- ✅ Export functionality
- ✅ Backup system

## Next Steps

1. Customize the configuration files as needed
2. Add your own data through the web interface
3. Modify the design or add new features
4. Set up version control (Git) for your changes

## Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check server error logs
4. Ensure all file permissions are correct

---

**Happy Coding! 🚀**


