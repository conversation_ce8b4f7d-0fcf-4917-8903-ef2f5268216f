<?php
session_start();

require_once 'classes/User.php';
require_once 'classes/Branch.php';

echo "<h2>Branch Login Debug</h2>";

// Debug information
echo "<h3>Debug Information:</h3>";
echo "<p><strong>Requested Branch ID:</strong> " . ($_GET['branch'] ?? 'Not provided') . "</p>";

try {
    $branch = new Branch();
    
    if (isset($_GET['branch'])) {
        $branch_id = $_GET['branch'];
        echo "<p><strong>Looking for Branch ID:</strong> $branch_id</p>";
        
        $branch_info = $branch->getBranchById($branch_id);
        
        if ($branch_info) {
            echo "<p style='color: green;'>✅ Branch found:</p>";
            echo "<ul>";
            echo "<li>ID: " . $branch_info['id'] . "</li>";
            echo "<li>Name: " . $branch_info['name'] . "</li>";
            echo "<li>Location: " . $branch_info['location'] . "</li>";
            echo "</ul>";
            
            echo "<h3>Login Form Should Display Below:</h3>";
            echo "<div style='border: 2px solid green; padding: 20px; margin: 20px 0;'>";
            echo "<h4>Login to " . htmlspecialchars($branch_info['name']) . " Branch</h4>";
            echo "<form method='POST'>";
            echo "<p><label>Username: <input type='text' name='username' required></label></p>";
            echo "<p><label>Password: <input type='password' name='password' required></label></p>";
            echo "<p><button type='submit'>Login</button></p>";
            echo "</form>";
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ Branch ID $branch_id not found</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>All Branches in Database:</h3>";
    $all_branches = $branch->getAllBranches();
    
    if (empty($all_branches)) {
        echo "<p style='color: red;'>❌ No branches found in database</p>";
        echo "<p><strong>Solution:</strong> Run <a href='setup_database.php'>setup_database.php</a> first</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($all_branches) . " branches:</p>";
        echo "<ul>";
        foreach ($all_branches as $branch_data) {
            echo "<li>ID: {$branch_data['id']} - {$branch_data['name']} ({$branch_data['location']})</li>";
        }
        echo "</ul>";
        
        echo "<h3>Test Direct Links:</h3>";
        echo "<ul>";
        foreach ($all_branches as $branch_data) {
            echo "<li><a href='branch_login.php?branch={$branch_data['id']}' target='_blank'>branch_login.php?branch={$branch_data['id']}</a> - {$branch_data['name']}</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Solution:</strong> Check database connection and run <a href='setup_database.php'>setup_database.php</a></p>";
}

echo "<hr>";
echo "<h3>Quick Fixes:</h3>";
echo "<ol>";
echo "<li>If no branches found: <a href='setup_database.php'>Run Database Setup</a></li>";
echo "<li>If database error: Check database configuration in config/database.php</li>";
echo "<li>If branch not found: Verify branch ID exists (should be 1, 2, 3, or 4)</li>";
echo "</ol>";

echo "<hr>";
echo "<h3>Expected URLs:</h3>";
echo "<ul>";
echo "<li><a href='branch_login.php?branch=1'>branch_login.php?branch=1</a> - Should show Uppala login</li>";
echo "<li><a href='branch_login.php?branch=2'>branch_login.php?branch=2</a> - Should show Seethangoli login</li>";
echo "<li><a href='branch_login.php?branch=3'>branch_login.php?branch=3</a> - Should show Kasaragod login</li>";
echo "<li><a href='branch_login.php?branch=4'>branch_login.php?branch=4</a> - Should show Kochi login</li>";
echo "</ul>";
?>
