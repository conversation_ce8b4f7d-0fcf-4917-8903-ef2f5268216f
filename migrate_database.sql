-- Migration script to add missing columns to existing database
-- Run this script if you're getting "Column not found: 'type'" error

-- Add type column to transactions table if it doesn't exist
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS type ENUM('income', 'expense') NOT NULL DEFAULT 'expense';

-- Add created_by column to transactions table if it doesn't exist  
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS created_by INT DEFAULT 1;

-- Update existing transactions to be 'expense' type (since they were previously all expenses)
UPDATE transactions SET type = 'expense' WHERE type = 'expense' OR type IS NULL;

-- Add some sample income transactions
INSERT INTO transactions (branch_id, amount, type, description, category, date, created_by) VALUES
(1, 15000.00, 'income', 'Monthly fees collection', 'Fees', '2024-01-23', 1),
(2, 8000.00, 'income', 'Special training fees', 'Special Training Fees', '2024-01-24', 1),
(3, 12000.00, 'income', 'School training fees', 'School Training', '2024-01-25', 1),
(4, 6000.00, 'income', 'Community training fees', 'Community Training', '2024-01-26', 1);

-- Verify the changes
SELECT 'Migration completed successfully' as status;


