<?php
require_once __DIR__ . '/../config/database.php';

class Transaction {
    private $conn;
    private $table = 'transactions';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Add new transaction
    public function addTransaction($branch_id, $amount, $type, $description, $category, $date, $created_by) {
        $query = "INSERT INTO " . $this->table . " 
                  (branch_id, amount, type, description, category, date, created_by) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$branch_id, $amount, $type, $description, $category, $date, $created_by]);
    }

    // Get transactions by branch (includes main transactions with branch_id = 0)
    public function getTransactionsByBranch($branch_id, $limit = null) {
        $query = "SELECT t.*, COALESCE(b.name, 'Main') as branch_name 
                  FROM " . $this->table . " t 
                  LEFT JOIN branches b ON t.branch_id = b.id 
                  WHERE t.branch_id = ? 
                  ORDER BY t.date DESC, t.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get all transactions (includes main transactions with branch_id = 0)
    public function getAllTransactions($filters = [], $limit = null) {
        $query = "SELECT t.*, COALESCE(b.name, 'Main') as branch_name 
                  FROM " . $this->table . " t 
                  LEFT JOIN branches b ON t.branch_id = b.id 
                  WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (isset($filters['branch']) && $filters['branch'] !== '') {
            if ($filters['branch'] == '0' || $filters['branch'] == 'main') {
                $query .= " AND t.branch_id IS NULL";
            } else {
                $query .= " AND t.branch_id = ?";
                $params[] = $filters['branch'];
            }
        }
        
        if (isset($filters['category']) && !empty($filters['category'])) {
            $query .= " AND t.category = ?";
            $params[] = $filters['category'];
        }
        
        if (isset($filters['start_date']) && !empty($filters['start_date'])) {
            $query .= " AND t.date >= ?";
            $params[] = $filters['start_date'];
        }
        
        if (isset($filters['end_date']) && !empty($filters['end_date'])) {
            $query .= " AND t.date <= ?";
            $params[] = $filters['end_date'];
        }
        
        $query .= " ORDER BY t.date DESC, t.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get transaction by ID (includes main transactions with branch_id = 0)
    public function getTransactionById($id) {
        $query = "SELECT t.*, COALESCE(b.name, 'Main') as branch_name 
                  FROM " . $this->table . " t 
                  LEFT JOIN branches b ON t.branch_id = b.id 
                  WHERE t.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Update transaction
    public function updateTransaction($id, $amount, $description, $category, $date) {
        $query = "UPDATE " . $this->table . " 
                  SET amount = ?, description = ?, category = ?, date = ? 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$amount, $description, $category, $date, $id]);
    }

    // Delete transaction
    public function deleteTransaction($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    // Get transactions by date range
    public function getTransactionsByDateRange($start_date, $end_date, $branch_id = null) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  WHERE e.date BETWEEN ? AND ?";
        
        $params = [$start_date, $end_date];
        
        if ($branch_id) {
            $query .= " AND e.branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " ORDER BY e.date DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get total expenses by branch
    public function getTotalExpensesByBranch($branch_id) {
        $query = "SELECT SUM(amount) as total FROM " . $this->table . " WHERE branch_id = ? AND type = 'expense'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    // Get total income by branch
    public function getTotalIncomeByBranch($branch_id) {
        $query = "SELECT SUM(amount) as total FROM " . $this->table . " WHERE branch_id = ? AND type = 'income'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    // Get total expenses (all branches)
    public function getTotalExpenses() {
        $query = "SELECT SUM(amount) as total FROM " . $this->table . " WHERE type = 'expense'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    // Get total income (all branches)
    public function getTotalIncome() {
        $query = "SELECT SUM(amount) as total FROM " . $this->table . " WHERE type = 'income'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    // Get net profit (income - expenses)
    public function getNetProfit() {
        $income = $this->getTotalIncome();
        $expenses = $this->getTotalExpenses();
        return $income - $expenses;
    }

    // Get transactions by category and type
    public function getTransactionsByCategory($branch_id = null) {
        $query = "SELECT category, type, SUM(amount) as total, COUNT(*) as count 
                  FROM " . $this->table;
        
        $params = [];
        if ($branch_id) {
            $query .= " WHERE branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " GROUP BY category, type ORDER BY type, total DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get expenses by category (for backward compatibility)
    public function getExpensesByCategory($branch_id = null) {
        $query = "SELECT category, SUM(amount) as total, COUNT(*) as count 
                  FROM " . $this->table . " WHERE type = 'expense'";
        
        $params = [];
        if ($branch_id) {
            $query .= " AND branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " GROUP BY category ORDER BY total DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get income by category
    public function getIncomeByCategory($branch_id = null) {
        $query = "SELECT category, SUM(amount) as total, COUNT(*) as count 
                  FROM " . $this->table . " WHERE type = 'income'";
        
        $params = [];
        if ($branch_id) {
            $query .= " AND branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " GROUP BY category ORDER BY total DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
