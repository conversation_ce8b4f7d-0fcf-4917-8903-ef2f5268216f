<?php
require_once __DIR__ . '/../config/database.php';

class User {
    private $conn;
    private $table = 'users';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Create new user
    public function createUser($username, $email, $password, $role, $branch_id, $first_name, $last_name, $phone = null) {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $query = "INSERT INTO " . $this->table . " 
                  (username, email, password, role, branch_id, first_name, last_name, phone) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$username, $email, $hashedPassword, $role, $branch_id, $first_name, $last_name, $phone]);
    }

    // Get user by username
    public function getUserByUsername($username) {
        $query = "SELECT u.*, b.name as branch_name 
                  FROM " . $this->table . " u 
                  LEFT JOIN branches b ON u.branch_id = b.id 
                  WHERE u.username = ? AND (u.status = 'active' OR u.status IS NULL)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$username]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get user by ID
    public function getUserById($id) {
        $query = "SELECT u.*, b.name as branch_name 
                  FROM " . $this->table . " u 
                  LEFT JOIN branches b ON u.branch_id = b.id 
                  WHERE u.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Verify user login
    public function verifyLogin($username, $password) {
        $user = $this->getUserByUsername($username);
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        return false;
    }

    // Get all users
    public function getAllUsers() {
        $query = "SELECT u.*, b.name as branch_name 
                  FROM " . $this->table . " u 
                  LEFT JOIN branches b ON u.branch_id = b.id 
                  ORDER BY u.role, u.first_name, u.last_name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get users by branch
    public function getUsersByBranch($branch_id) {
        $query = "SELECT u.*, b.name as branch_name 
                  FROM " . $this->table . " u 
                  LEFT JOIN branches b ON u.branch_id = b.id 
                  WHERE u.branch_id = ?
                  ORDER BY u.role, u.username";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Update user
    public function updateUser($id, $username, $email, $role, $branch_id, $first_name, $last_name, $phone = null, $status = 'active') {
        $query = "UPDATE " . $this->table . " 
                  SET username = ?, email = ?, role = ?, branch_id = ?, 
                      first_name = ?, last_name = ?, phone = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$username, $email, $role, $branch_id, $first_name, $last_name, $phone, $status, $id]);
    }

    // Update user password
    public function updatePassword($id, $new_password) {
        $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
        
        $query = "UPDATE " . $this->table . " 
                  SET password = ?, updated_at = CURRENT_TIMESTAMP 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$hashedPassword, $id]);
    }

    // Delete user (soft delete)
    public function deleteUser($id) {
        $query = "UPDATE " . $this->table . " 
                  SET status = 'inactive', updated_at = CURRENT_TIMESTAMP 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    // Check if username exists
    public function usernameExists($username, $exclude_id = null) {
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE username = ?";
        $params = [$username];
        
        if ($exclude_id) {
            $query .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }

    // Check if email exists
    public function emailExists($email, $exclude_id = null) {
        // Don't check for empty emails
        if (empty($email)) {
            return false;
        }
        
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE email = ?";
        $params = [$email];
        
        if ($exclude_id) {
            $query .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }

    // Get user statistics
    public function getUserStats() {
        $query = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
                    SUM(CASE WHEN role = 'branch_manager' THEN 1 ELSE 0 END) as manager_count,
                    SUM(CASE WHEN role = 'branch_staff' THEN 1 ELSE 0 END) as staff_count,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users
                  FROM " . $this->table;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>

