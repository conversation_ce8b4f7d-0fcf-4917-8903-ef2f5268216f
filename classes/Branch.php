<?php
require_once __DIR__ . '/../config/database.php';

class Branch {
    private $conn;
    private $table = 'branches';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Create new branch
    public function createBranch($name, $location, $manager_name = null, $phone = null, $email = null) {
        $query = "INSERT INTO " . $this->table . " 
                  (name, location, manager_name, contact_phone, contact_email) 
                  VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$name, $location, $manager_name, $phone, $email]);
    }

    // Get all branches
    public function getAllBranches() {
        $query = "SELECT * FROM " . $this->table . " ORDER BY name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get branch by ID
    public function getBranchById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Update branch
    public function updateBranch($id, $name, $location, $manager_name = null, $phone = null, $email = null) {
        $query = "UPDATE " . $this->table . " 
                  SET name = ?, location = ?, manager_name = ?, contact_phone = ?, contact_email = ?, updated_at = CURRENT_TIMESTAMP
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$name, $location, $manager_name, $phone, $email, $id]);
    }

    // Delete branch
    public function deleteBranch($id) {
        // First check if there are any users assigned to this branch
        $userQuery = "SELECT COUNT(*) FROM users WHERE branch_id = ?";
        $stmt = $this->conn->prepare($userQuery);
        $stmt->execute([$id]);
        $userCount = $stmt->fetchColumn();
        
        if ($userCount > 0) {
            throw new Exception("Cannot delete branch. There are users assigned to this branch.");
        }
        
        // Check if there are any transactions for this branch
        $transactionQuery = "SELECT COUNT(*) FROM transactions WHERE branch_id = ?";
        $stmt = $this->conn->prepare($transactionQuery);
        $stmt->execute([$id]);
        $transactionCount = $stmt->fetchColumn();
        
        if ($transactionCount > 0) {
            throw new Exception("Cannot delete branch. There are transactions associated with this branch.");
        }
        
        // If no dependencies, delete the branch
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    // Get branch statistics
    public function getBranchStats($branch_id) {
        require_once __DIR__ . '/Transaction.php';
        require_once __DIR__ . '/Student.php';
        require_once __DIR__ . '/Enquiry.php';
        
        $transaction = new Transaction();
        $student = new Student();
        $enquiry = new Enquiry();
        
        return [
            'total_expenses' => $transaction->getTotalExpensesByBranch($branch_id),
            'total_income' => $transaction->getTotalIncomeByBranch($branch_id),
            'student_count' => count($student->getStudentsByBranch($branch_id)),
            'enquiry_count' => count($enquiry->getEnquiriesByBranch($branch_id)),
            'user_count' => count($this->getUsersByBranch($branch_id))
        ];
    }

    // Get users assigned to branch
    public function getUsersByBranch($branch_id) {
        require_once __DIR__ . '/User.php';
        $user = new User();
        return $user->getUsersByBranch($branch_id);
    }

    // Check if branch name exists
    public function branchNameExists($name, $exclude_id = null) {
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE name = ?";
        $params = [$name];
        
        if ($exclude_id) {
            $query .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }
}
?>

