<?php
session_start();
require_once 'classes/User.php';

echo "<h2>Create Admin User</h2>";

try {
    $user = new User();
    
    // Check if admin already exists
    $existingAdmin = $user->getUserByUsername('admin');
    
    if ($existingAdmin) {
        echo "<p style='color: orange;'>⚠️ Admin user already exists!</p>";
        echo "<p><strong>Username:</strong> " . $existingAdmin['username'] . "</p>";
        echo "<p><strong>Role:</strong> " . $existingAdmin['role'] . "</p>";
        echo "<p><strong>Status:</strong> " . ($existingAdmin['status'] ?? 'active') . "</p>";
        
        // Update admin user to ensure it has correct role
        if ($existingAdmin['role'] !== 'admin') {
            echo "<p>Updating user role to admin...</p>";
            $user->updateUser(
                $existingAdmin['id'],
                'admin',
                $existingAdmin['email'],
                'admin',
                null,
                'Admin',
                'User',
                $existingAdmin['phone'] ?? '9999999999'
            );
            echo "<p style='color: green;'>✓ Role updated to admin</p>";
        }
        
        // Reset password to default
        echo "<p>Resetting password to default...</p>";
        $user->updatePassword($existingAdmin['id'], 'admin123');
        echo "<p style='color: green;'>✓ Password reset to 'admin123'</p>";
        
    } else {
        echo "<p>Creating new admin user...</p>";
        
        // Create admin user
        $result = $user->createUser(
            'admin',
            '<EMAIL>',
            'admin123',
            'admin',
            null, // No branch for admin
            'Admin',
            'User',
            '9999999999'
        );
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin user created successfully!</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin user</p>";
        }
    }
    
    echo "<hr>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: green; margin-top: 0;'>🎉 Admin User Ready!</h4>";
    echo "<p><strong>Username:</strong> admin</p>";
    echo "<p><strong>Password:</strong> admin123</p>";
    echo "<p><strong>Role:</strong> admin</p>";
    echo "<p>You can now log in and access all branch management features.</p>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='login.php' style='background: #22c55e; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Login as Admin</a>";
    echo "<a href='branches.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Branches</a>";
    echo "<a href='check_admin.php' style='background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Check Status</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>The database might not be set up yet. Please run <a href='setup_database.php'>setup_database.php</a> first.</p>";
}
?>
