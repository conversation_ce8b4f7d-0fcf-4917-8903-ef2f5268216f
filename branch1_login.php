<?php
session_start();

require_once 'classes/User.php';

$error = '';
$branch_name = 'Uppala';
$branch_id = 1;

// Handle login form submission
if (isset($_POST['username']) && isset($_POST['password'])) {
    // Branch 1 specific credentials
    $valid_username = 'uppala_branch';
    $valid_password = 'uppala2024';
    
    if ($_POST['username'] === $valid_username && $_POST['password'] === $valid_password) {
        // Store user session
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = $valid_username;
        $_SESSION['role'] = 'branch_manager';
        $_SESSION['branch_id'] = $branch_id;
        $_SESSION['first_name'] = 'Uppala';
        $_SESSION['last_name'] = 'Manager';
        $_SESSION['branch_name'] = $branch_name;
        $_SESSION['branch_authenticated'] = true;
        $_SESSION['login_time'] = time();
        
        header('Location: branch_dashboard.php?branch=' . $branch_id);
        exit;
    } else {
        $error = 'Invalid username or password for Uppala Branch';
    }
}

// If already logged in, redirect to dashboard
if (isset($_SESSION['user_id']) && isset($_SESSION['branch_authenticated']) && $_SESSION['branch_id'] == $branch_id) {
    header('Location: branch_dashboard.php?branch=' . $branch_id);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Uppala Branch Login - KFT Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }
        
        .login-header {
            background: white;
            padding: 2rem 2rem 1rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .login-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 0.5rem 0;
        }
        
        .login-header p {
            color: #718096;
            font-size: 0.875rem;
            margin: 0;
        }
        
        .branch-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .branch-info h5 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
        }
        
        .branch-info p {
            color: #718096;
            font-size: 0.75rem;
            margin: 0;
        }
        
        .login-form {
            padding: 1.5rem 2rem 2rem;
        }
        
        .form-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }
        
        .form-control:focus {
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
            outline: none;
        }
        
        .input-group-text {
            background: #f7fafc;
            border: 1px solid #d1d5db;
            border-right: none;
            color: #718096;
            border-radius: 6px 0 0 6px;
        }
        
        .btn-login {
            background: #3182ce;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            font-size: 0.875rem;
            color: white;
            width: 100%;
            transition: background-color 0.2s ease;
        }
        
        .btn-login:hover {
            background: #2c5aa0;
        }
        
        .alert {
            border-radius: 6px;
            border: none;
            font-size: 0.875rem;
            padding: 0.75rem 1rem;
        }
        
        .credentials-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .credentials-info h6 {
            font-size: 0.75rem;
            font-weight: 600;
            color: #0369a1;
            margin-bottom: 0.5rem;
        }
        
        .credentials-info small {
            color: #0369a1;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h2>Uppala Branch Login</h2>
                <p>Access your branch dashboard</p>
            </div>
            
            <div class="branch-info">
                <h5>Uppala Branch</h5>
                <p>Kerala, India</p>
            </div>
            
            <div class="login-form">
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-login">
                        Access Uppala Dashboard
                    </button>
                </form>
                
                <div class="credentials-info">
                    <h6>Branch Credentials</h6>
                    <small><strong>Username:</strong> uppala_branch<br>
                    <strong>Password:</strong> uppala2024</small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>