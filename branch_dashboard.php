<?php
session_start();

// Check if user is logged in and has branch authentication
if (!isset($_SESSION['user_id']) || !isset($_SESSION['branch_authenticated'])) {
    header('Location: branch_login.php');
    exit;
}

// Check session timeout (8 hours)
if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 28800) {
    session_destroy();
    header('Location: branch_login.php?timeout=1');
    exit;
}

// Verify user has access to the requested branch
$requested_branch = $_GET['branch'] ?? $_SESSION['branch_id'];
if ($_SESSION['role'] !== 'admin' && $_SESSION['branch_id'] != $requested_branch) {
    header('Location: branch_dashboard.php?branch=' . $_SESSION['branch_id']);
    exit;
}

require_once 'classes/Dashboard.php';
require_once 'classes/Transaction.php';
require_once 'classes/Student.php';
require_once 'classes/Enquiry.php';

$branch_id = $_GET['branch'] ?? 1;
$branch_name = '';

// Get branch name
$branches = [
    1 => 'Uppala',
    2 => 'Seethangoli', 
    3 => 'Kasaragod',
    4 => 'Kochi'
];

$branch_name = $branches[$branch_id] ?? 'Unknown Branch';

$dashboard = new Dashboard();
$transaction = new Transaction();
$student = new Student();
$enquiry = new Enquiry();

// Get branch-specific data
$branch_expenses = $transaction->getTotalExpensesByBranch($branch_id);
$branch_transactions = $transaction->getTransactionsByBranch($branch_id, 10);
$branch_students = $student->getStudentsByBranch($branch_id);
$branch_enquiries = $enquiry->getEnquiriesByBranch($branch_id);
$attendance_stats = $student->getAttendanceStats($branch_id);
$enquiry_stats = $enquiry->getEnquiryStats($branch_id);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $branch_name; ?> Branch Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand premium-brand" href="index.php">
                <div class="brand-text">
                    <span class="brand-main">KFT Manager</span>
                </div>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php" title="Main Dashboard">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="branch_dashboard.php?branch=<?php echo $branch_id; ?>" title="<?php echo $branch_name; ?> Branch">
                            <i class="fas fa-building"></i>
                        </a>
                    </li>
                    <?php if (isset($_SESSION['role']) && ($_SESSION['role'] === 'branch_manager' || $_SESSION['role'] === 'admin')): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="branch_user_management.php" title="User Management">
                            <i class="fas fa-users"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php" title="Transactions">
                            <i class="fas fa-receipt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php" title="Settings">
                            <i class="fas fa-cog"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php?branch=1" title="Logout" onclick="return confirm('Are you sure you want to logout?')">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-container" style="margin-top: 85px;">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-building text-primary me-2"></i><?php echo $branch_name; ?> Branch Dashboard
                        </h1>
                        <p class="text-muted">Welcome, <?php echo htmlspecialchars(($_SESSION['first_name'] ?? 'User') . ' ' . ($_SESSION['last_name'] ?? '')); ?> - Manage your branch operations</p>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-primary fs-6 mb-2"><?php echo ucfirst(str_replace('_', ' ', $_SESSION['role'] ?? 'User')); ?></div>
                        <div class="text-muted small">Branch ID: <?php echo $branch_id; ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Branch Expenses</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    ₹<?php echo number_format($branch_expenses, 2); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total Students</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo count($branch_students); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    New Enquiries</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo $enquiry_stats['new_enquiries']; ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Today's Attendance</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo $attendance_stats['present_count']; ?>/<?php echo $attendance_stats['total_students']; ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <ul class="nav nav-tabs mb-4" id="branchTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="students-tab" data-bs-toggle="tab" data-bs-target="#students" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>Students & Attendance
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="enquiries-tab" data-bs-toggle="tab" data-bs-target="#enquiries" type="button" role="tab">
                    <i class="fas fa-question-circle me-2"></i>Smart Enquiries
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="expenses-tab" data-bs-toggle="tab" data-bs-target="#expenses" type="button" role="tab">
                    <i class="fas fa-receipt me-2"></i>Branch Expenses
                </button>
            </li>
            <?php if (isset($_SESSION['role']) && ($_SESSION['role'] === 'branch_manager' || $_SESSION['role'] === 'admin')): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>User Management
                </button>
            </li>
            <?php endif; ?>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="branchTabsContent">
            <!-- Students & Attendance Tab -->
            <div class="tab-pane fade show active" id="students" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">Student Management</h6>
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                                    <i class="fas fa-plus me-2"></i>Add Student
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Roll No.</th>
                                                <th>Name</th>
                                                <th>Class</th>
                                                <th>Phone</th>
                                                <th>Email</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if(empty($branch_students)): ?>
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">No students found</td>
                                            </tr>
                                            <?php else: ?>
                                            <?php foreach($branch_students as $student_data): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($student_data['roll_number']); ?></td>
                                                <td><?php echo htmlspecialchars($student_data['name']); ?></td>
                                                <td><?php echo htmlspecialchars($student_data['class']); ?></td>
                                                <td><?php echo htmlspecialchars($student_data['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($student_data['email']); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="markAttendance(<?php echo $student_data['id']; ?>)">
                                                            <i class="fas fa-user-check"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" onclick="viewStudent(<?php echo $student_data['id']; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger btn-delete" onclick="deleteStudent(<?php echo $student_data['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Smart Enquiries Tab -->
            <div class="tab-pane fade" id="enquiries" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">Smart Enquiries System</h6>
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addEnquiryModal">
                                    <i class="fas fa-plus me-2"></i>Add Enquiry
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Phone</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Priority</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if(empty($branch_enquiries)): ?>
                                            <tr>
                                                <td colspan="7" class="text-center text-muted">No enquiries found</td>
                                            </tr>
                                            <?php else: ?>
                                            <?php foreach($branch_enquiries as $enquiry): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($enquiry['name']); ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['subject']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $enquiry['status'] === 'new' ? 'danger' : 
                                                            ($enquiry['status'] === 'contacted' ? 'warning' : 
                                                            ($enquiry['status'] === 'converted' ? 'success' : 'secondary')); 
                                                    ?>">
                                                        <?php echo ucfirst($enquiry['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $enquiry['priority'] === 'high' ? 'danger' : 
                                                            ($enquiry['priority'] === 'medium' ? 'warning' : 'success'); 
                                                    ?>">
                                                        <?php echo ucfirst($enquiry['priority']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($enquiry['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewEnquiry(<?php echo $enquiry['id']; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-success" onclick="updateEnquiryStatus(<?php echo $enquiry['id']; ?>, 'contacted')">
                                                            <i class="fas fa-phone"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger btn-delete" onclick="deleteEnquiry(<?php echo $enquiry['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Branch Expenses Tab -->
            <div class="tab-pane fade" id="expenses" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">Branch Expenses</h6>
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                                    <i class="fas fa-plus me-2"></i>Add Expense
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Description</th>
                                                <th>Category</th>
                                                <th>Amount</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if(empty($branch_transactions)): ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No expenses found</td>
                                            </tr>
                                            <?php else: ?>
                                            <?php foreach($branch_transactions as $transaction): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($transaction['date'] ?? date('Y-m-d'))); ?></td>
                                                <td><?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($transaction['category'] ?? 'Uncategorized'); ?></span>
                                                </td>
                                                <td class="fw-bold">₹<?php echo number_format($transaction['amount'] ?? 0, 2); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="editExpense(<?php echo $transaction['id']; ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger btn-delete" onclick="deleteExpense(<?php echo $transaction['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- User Management Tab -->
            <?php if (isset($_SESSION['role']) && ($_SESSION['role'] === 'branch_manager' || $_SESSION['role'] === 'admin')): ?>
            <div class="tab-pane fade" id="users" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">Branch User Management</h6>
                                <div class="btn-group" role="group">
                                    <a href="branch_user_management.php" class="btn btn-primary btn-sm">
                                        <i class="fas fa-users me-2"></i>Full User Management
                                    </a>
                                    <button class="btn btn-success btn-sm" onclick="quickAddUser()">
                                        <i class="fas fa-user-plus me-2"></i>Quick Add User
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <?php 
                                require_once 'classes/User.php';
                                $user_class = new User();
                                $branch_users = $user_class->getUsersByBranch($branch_id);
                                ?>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>User</th>
                                                        <th>Role</th>
                                                        <th>Status</th>
                                                        <th>Last Login</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if (empty($branch_users)): ?>
                                                    <tr>
                                                        <td colspan="5" class="text-center text-muted py-4">
                                                            <i class="fas fa-users fa-2x mb-3 text-muted"></i><br>
                                                            No users found for this branch.
                                                        </td>
                                                    </tr>
                                                    <?php else: ?>
                                                    <?php foreach ($branch_users as $user_data): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="user-avatar me-2">
                                                                    <i class="fas fa-user-circle fa-lg text-primary"></i>
                                                                </div>
                                                                <div>
                                                                    <strong><?php echo htmlspecialchars($user_data['username']); ?></strong><br>
                                                                    <small class="text-muted"><?php echo htmlspecialchars($user_data['first_name'] . ' ' . $user_data['last_name']); ?></small>
                                                                    <?php if ($user_data['id'] == $_SESSION['user_id']): ?>
                                                                    <span class="badge bg-info ms-2">You</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?php 
                                                                echo $user_data['role'] === 'branch_manager' ? 'warning' : 'info'; 
                                                            ?>">
                                                                <?php echo ucfirst(str_replace('_', ' ', $user_data['role'])); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?php echo ($user_data['status'] ?? 'active') === 'active' ? 'success' : 'secondary'; ?>">
                                                                <?php echo ucfirst($user_data['status'] ?? 'active'); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">
                                                                <?php echo isset($user_data['last_login']) ? date('M d, Y', strtotime($user_data['last_login'])) : 'Never'; ?>
                                                            </small>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <button class="btn btn-sm btn-outline-primary" onclick="quickEditUser(<?php echo $user_data['id']; ?>)" title="Quick Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button class="btn btn-sm btn-outline-warning" onclick="quickChangePassword(<?php echo $user_data['id']; ?>)" title="Change Password">
                                                                    <i class="fas fa-key"></i>
                                                                </button>
                                                                <?php if ($user_data['id'] != $_SESSION['user_id']): ?>
                                                                <button class="btn btn-sm btn-outline-danger" onclick="quickDeleteUser(<?php echo $user_data['id']; ?>)" title="Delete User">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <i class="fas fa-users fa-3x text-primary mb-3"></i>
                                                <h5>User Statistics</h5>
                                                <div class="row text-center mt-3">
                                                    <div class="col-6">
                                                        <div class="border-end">
                                                            <h4 class="text-warning"><?php echo count(array_filter($branch_users, function($u) { return $u['role'] === 'branch_manager'; })); ?></h4>
                                                            <small class="text-muted">Managers</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <h4 class="text-info"><?php echo count(array_filter($branch_users, function($u) { return $u['role'] === 'branch_staff'; })); ?></h4>
                                                        <small class="text-muted">Staff</small>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="d-grid">
                                                    <a href="branch_user_management.php" class="btn btn-primary">
                                                        <i class="fas fa-cog me-2"></i>Advanced Management
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light bg-white d-lg-none border-top">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branch_dashboard.php?branch=<?php echo $branch_id; ?>" class="nav-link active">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branch</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // Branch-specific JavaScript functions
        function markAttendance(studentId) {
            // Implementation for marking attendance
            console.log('Mark attendance for student:', studentId);
        }
        
        function viewStudent(studentId) {
            // Implementation for viewing student details
            console.log('View student:', studentId);
        }
        
        function deleteStudent(studentId) {
            if (confirm('Are you sure you want to delete this student?')) {
                // Implementation for deleting student
                console.log('Delete student:', studentId);
            }
        }
        
        function viewEnquiry(enquiryId) {
            // Implementation for viewing enquiry details
            console.log('View enquiry:', enquiryId);
        }
        
        function updateEnquiryStatus(enquiryId, status) {
            // Implementation for updating enquiry status
            console.log('Update enquiry status:', enquiryId, status);
        }
        
        function deleteEnquiry(enquiryId) {
            if (confirm('Are you sure you want to delete this enquiry?')) {
                // Implementation for deleting enquiry
                console.log('Delete enquiry:', enquiryId);
            }
        }
        
        function editExpense(expenseId) {
            // Implementation for editing expense
            console.log('Edit expense:', expenseId);
        }
        
        function deleteExpense(expenseId) {
            if (confirm('Are you sure you want to delete this expense?')) {
                // Implementation for deleting expense
                console.log('Delete expense:', expenseId);
            }
        }
        
        // User Management Functions
        function quickAddUser() {
            window.location.href = 'branch_user_management.php';
        }
        
        function quickEditUser(userId) {
            // Redirect to user management with edit mode
            window.location.href = 'branch_user_management.php?edit=' + userId;
        }
        
        function quickChangePassword(userId) {
            if (confirm('Change password for this user?')) {
                // Redirect to user management with password change mode
                window.location.href = 'branch_user_management.php?password=' + userId;
            }
        }
        
        function quickDeleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                // Redirect to user management with delete confirmation
                window.location.href = 'branch_user_management.php?delete=' + userId;
            }
        }
    </script>

    <!-- Floating Action Button -->
    <div class="fab-container">
        <a href="transactions.php" class="fab-btn" title="Add Transaction">
            <i class="fas fa-plus"></i>
        </a>
    </div>
</body>
</html>
