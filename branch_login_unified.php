<?php
session_start();

require_once 'classes/User.php';
require_once 'classes/Branch.php';

$error = '';
$branch_info = null;
$selected_branch_id = null;

// Handle branch selection from URL parameter
if (isset($_GET['branch'])) {
    $selected_branch_id = (int)$_GET['branch'];
    $branch = new Branch();
    $branch_info = $branch->getBranchById($selected_branch_id);
    
    if (!$branch_info) {
        $error = 'Invalid branch selected. Branch ID ' . $selected_branch_id . ' not found.';
        $branch_info = null;
    }
}

// Handle login form submission
if (isset($_POST['username']) && isset($_POST['password'])) {
    $user = new User();
    $loginUser = $user->verifyLogin($_POST['username'], $_POST['password']);
    
    if ($loginUser) {
        // Verify user belongs to the selected branch
        if ($branch_info && $loginUser['branch_id'] != $branch_info['id']) {
            $error = 'This user does not belong to the selected branch.';
        } else {
            // Store user session with branch authentication
            $_SESSION['user_id'] = $loginUser['id'];
            $_SESSION['username'] = $loginUser['username'];
            $_SESSION['role'] = $loginUser['role'];
            $_SESSION['branch_id'] = $loginUser['branch_id'];
            $_SESSION['first_name'] = $loginUser['first_name'];
            $_SESSION['last_name'] = $loginUser['last_name'];
            $_SESSION['branch_name'] = $loginUser['branch_name'];
            $_SESSION['branch_authenticated'] = true;
            $_SESSION['login_time'] = time();
            
            // Redirect based on user role and branch
            if ($loginUser['role'] === 'admin') {
                header('Location: index.php');
            } else {
                header('Location: branch_dashboard.php?branch=' . $loginUser['branch_id']);
            }
            exit;
        }
    } else {
        $error = 'Invalid username or password for this branch';
    }
}

// If already logged in, redirect to appropriate dashboard
if (isset($_SESSION['user_id']) && isset($_SESSION['branch_authenticated'])) {
    if ($_SESSION['role'] === 'admin') {
        header('Location: index.php');
    } else {
        header('Location: branch_dashboard.php?branch=' . $_SESSION['branch_id']);
    }
    exit;
}

// Get all branches for selection
$branch = new Branch();
$all_branches = $branch->getAllBranches();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $branch_info ? $branch_info['name'] . ' Branch Login' : 'Branch Login'; ?> - KFT Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        body {
            background: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }
        
        .login-header {
            background: white;
            padding: 2rem 2rem 1rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .login-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 0.5rem 0;
        }
        
        .login-header p {
            color: #718096;
            font-size: 0.875rem;
            margin: 0;
        }
        
        .branch-selector {
            padding: 1.5rem 2rem;
        }
        
        .branch-selector h4 {
            font-size: 1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .branch-card {
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            text-align: center;
            background: white;
            margin-bottom: 0.75rem;
        }
        
        .branch-card:hover {
            border-color: #3182ce;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .branch-card.selected {
            border-color: #3182ce;
            background: #ebf8ff;
        }
        
        .branch-card h6 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
        }
        
        .branch-card small {
            color: #718096;
            font-size: 0.75rem;
        }
        
        .login-form {
            padding: 1.5rem 2rem 2rem;
        }
        
        .form-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }
        
        .form-control:focus {
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
            outline: none;
        }
        
        .input-group-text {
            background: #f7fafc;
            border: 1px solid #d1d5db;
            border-right: none;
            color: #718096;
            border-radius: 6px 0 0 6px;
        }
        
        .btn-login {
            background: #3182ce;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            font-size: 0.875rem;
            color: white;
            width: 100%;
            transition: background-color 0.2s ease;
        }
        
        .btn-login:hover {
            background: #2c5aa0;
        }
        
        .branch-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .branch-info h5 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
        }
        
        .branch-info p {
            color: #718096;
            font-size: 0.75rem;
            margin: 0;
        }
        
        .alert {
            border-radius: 6px;
            border: none;
            font-size: 0.875rem;
            padding: 0.75rem 1rem;
        }
        
        .btn-outline-primary {
            border: 1px solid #3182ce;
            color: #3182ce;
            background: white;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .btn-outline-primary:hover {
            background: #3182ce;
            color: white;
        }
        
        .quick-access {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .quick-access h6 {
            font-size: 0.75rem;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.75rem;
            text-align: center;
        }
        
        .quick-access .btn {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            margin: 0.25rem;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-muted {
            color: #718096;
        }
        
        .small {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h2>KFT Branch Login</h2>
                <p>
                    <?php if ($branch_info): ?>
                        Access <?php echo htmlspecialchars($branch_info['name']); ?> Dashboard
                    <?php else: ?>
                        Select your branch to continue
                    <?php endif; ?>
                </p>
            </div>
                    
            <?php if (!$branch_info): ?>
            <!-- Branch Selection -->
            <div class="branch-selector">
                <?php if (isset($_GET['timeout'])): ?>
                <div class="alert alert-info">
                    Your session has expired. Please login again.
                </div>
                <?php elseif (isset($_GET['message']) && $_GET['message'] === 'branch_users_must_login_through_branch_login'): ?>
                <div class="alert alert-warning">
                    Branch users must login through the branch login system.
                </div>
                <?php elseif (isset($_GET['branch'])): ?>
                <div class="alert alert-warning">
                    Branch ID <?php echo htmlspecialchars($_GET['branch']); ?> not found.
                </div>
                <?php endif; ?>
                
                <h4>Select Your Branch</h4>
                
                <?php foreach($all_branches as $branch): ?>
                <div class="branch-card" onclick="selectBranch(<?php echo $branch['id']; ?>, '<?php echo htmlspecialchars($branch['name']); ?>')">
                    <h6><?php echo htmlspecialchars($branch['name']); ?></h6>
                    <small><?php echo htmlspecialchars($branch['location']); ?></small>
                </div>
                <?php endforeach; ?>
                
                <!-- Quick Access Links -->
                <div class="quick-access">
                    <h6>Quick Access</h6>
                    <?php foreach($all_branches as $branch): ?>
                    <a href="branch_login_unified.php?branch=<?php echo $branch['id']; ?>" class="btn btn-outline-primary">
                        <?php echo htmlspecialchars($branch['name']); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <!-- Branch Info -->
            <div class="branch-info">
                <h5><?php echo htmlspecialchars($branch_info['name']); ?> Branch</h5>
                <p><?php echo htmlspecialchars($branch_info['location']); ?></p>
            </div>
            <?php endif; ?>
            
            <?php if ($branch_info): ?>
            <!-- Login Form -->
            <div class="login-form">
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-login">
                        Access <?php echo htmlspecialchars($branch_info['name']); ?> Dashboard
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function selectBranch(branchId, branchName) {
            window.location.href = `branch_login_unified.php?branch=${branchId}`;
        }
    </script>
</body>
</html>
