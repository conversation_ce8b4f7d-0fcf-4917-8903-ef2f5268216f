<?php
session_start();
require_once 'classes/User.php';

echo "<h2>Admin User Status Check</h2>";

// Check current session
if (isset($_SESSION['user_id'])) {
    echo "<p><strong>Current User:</strong> " . ($_SESSION['username'] ?? 'Unknown') . "</p>";
    echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
    echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
    echo "<p><strong>Branch ID:</strong> " . ($_SESSION['branch_id'] ?? 'Not set') . "</p>";
} else {
    echo "<p><strong>Status:</strong> Not logged in</p>";
}

echo "<hr>";

// Check if admin user exists
try {
    $user = new User();
    $adminUser = $user->getUserByUsername('admin');
    
    if ($adminUser) {
        echo "<p><strong>Admin User:</strong> Found</p>";
        echo "<p><strong>Username:</strong> " . $adminUser['username'] . "</p>";
        echo "<p><strong>Role:</strong> " . $adminUser['role'] . "</p>";
        echo "<p><strong>Email:</strong> " . $adminUser['email'] . "</p>";
        echo "<p><strong>Status:</strong> " . ($adminUser['status'] ?? 'active') . "</p>";
    } else {
        echo "<p><strong>Admin User:</strong> Not found</p>";
        echo "<p>Creating admin user...</p>";
        
        // Create admin user
        $result = $user->createUser(
            'admin',
            '<EMAIL>',
            'admin123',
            'admin',
            null, // No branch for admin
            'Admin',
            'User',
            '9999999999'
        );
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin user created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin user</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Database might not be set up. Please run <a href='setup_database.php'>setup_database.php</a> first.</p>";
}

echo "<hr>";

// Show all users
try {
    $user = new User();
    $allUsers = $user->getAllUsers();
    
    echo "<h3>All Users in System:</h3>";
    if (empty($allUsers)) {
        echo "<p>No users found in database.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Branch</th><th>Status</th></tr>";
        foreach ($allUsers as $u) {
            echo "<tr>";
            echo "<td>" . $u['id'] . "</td>";
            echo "<td>" . $u['username'] . "</td>";
            echo "<td>" . $u['email'] . "</td>";
            echo "<td>" . $u['role'] . "</td>";
            echo "<td>" . ($u['branch_name'] ?? 'None') . "</td>";
            echo "<td>" . ($u['status'] ?? 'active') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading users: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>Go to Login</a> | <a href='index.php'>Go to Dashboard</a> | <a href='branches.php'>Go to Branches</a></p>";
?>
