<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'classes/Branch.php';
require_once 'classes/User.php';
require_once 'classes/Transaction.php';
require_once 'classes/Student.php';
require_once 'classes/Enquiry.php';

$branch = new Branch();
$user = new User();
$transaction = new Transaction();
$student = new Student();
$enquiry = new Enquiry();

// Get all branches
$all_branches = $branch->getAllBranches();

// Handle user management actions
$message = '';
$message_type = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_user':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = $_POST['password'];
                $first_name = trim($_POST['first_name']);
                $last_name = trim($_POST['last_name']);
                $role = $_POST['role'];
                $branch_id = $_POST['branch_id'];
                $phone = trim($_POST['phone'] ?? '');
                
                if ($user->usernameExists($username)) {
                    $message = 'Username already exists!';
                    $message_type = 'danger';
                } else {
                    $result = $user->createUser($username, $email, $password, $role, $branch_id, $first_name, $last_name, $phone);
                    if ($result) {
                        $message = 'User created successfully!';
                        $message_type = 'success';
                    } else {
                        $message = 'Failed to create user.';
                        $message_type = 'danger';
                    }
                }
                break;
                
            case 'update_password':
                $user_id = $_POST['user_id'];
                $new_password = $_POST['new_password'];
                
                $result = $user->updatePassword($user_id, $new_password);
                if ($result) {
                    $message = 'Password updated successfully!';
                    $message_type = 'success';
                } else {
                    $message = 'Failed to update password.';
                    $message_type = 'danger';
                }
                break;
                
            case 'delete_user':
                $user_id = $_POST['user_id'];
                
                if ($user_id == $_SESSION['user_id']) {
                    $message = 'You cannot delete your own account.';
                    $message_type = 'danger';
                } else {
                    $result = $user->deleteUser($user_id);
                    if ($result) {
                        $message = 'User deleted successfully!';
                        $message_type = 'success';
                    } else {
                        $message = 'Failed to delete user.';
                        $message_type = 'danger';
                    }
                }
                break;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Branches Dashboard - KFT Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .branch-card {
            transition: all 0.3s ease;
            border-left: 4px solid #22c55e;
        }
        .branch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .user-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .user-item {
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
            transition: background 0.2s ease;
        }
        .user-item:hover {
            background: #f8f9fa;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand premium-brand" href="index.php">
                <div class="brand-text">
                    <span class="brand-main">KFT Manager</span>
                </div>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php" title="Main Dashboard">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="all_branches_dashboard.php" title="All Branches">
                            <i class="fas fa-building"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php" title="Transactions">
                            <i class="fas fa-receipt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php" title="Settings">
                            <i class="fas fa-cog"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php" title="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-container" style="margin-top: 85px;">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-building text-primary me-2"></i>All Branches Dashboard
                        </h1>
                        <p class="text-muted">Manage users and view data for all branches</p>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-primary fs-6 mb-2"><?php echo ucfirst(str_replace('_', ' ', $_SESSION['role'] ?? 'User')); ?></div>
                        <div class="text-muted small">Total Branches: <?php echo count($all_branches); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Alert -->
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Branches Grid -->
        <div class="row">
            <?php foreach ($all_branches as $branch_data): ?>
            <?php
            $branch_id = $branch_data['id'];
            $branch_users = $user->getUsersByBranch($branch_id);
            $branch_expenses = $transaction->getTotalExpensesByBranch($branch_id);
            $branch_students = $student->getStudentsByBranch($branch_id);
            $branch_enquiries = $enquiry->getEnquiriesByBranch($branch_id);
            ?>
            <div class="col-lg-6 mb-4">
                <div class="card branch-card shadow h-100">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-building text-primary me-2"></i>
                                <?php echo htmlspecialchars($branch_data['name']); ?> Branch
                            </h5>
                            <button class="btn btn-primary btn-sm" onclick="openCreateUserModal(<?php echo $branch_id; ?>, '<?php echo htmlspecialchars($branch_data['name']); ?>')">
                                <i class="fas fa-user-plus me-1"></i>Add User
                            </button>
                        </div>
                        <small class="text-muted"><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($branch_data['location']); ?></small>
                    </div>
                    <div class="card-body">
                        <!-- Branch Stats -->
                        <div class="row mb-3">
                            <div class="col-6 col-md-3 mb-2">
                                <div class="stat-card text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h4><?php echo count($branch_users); ?></h4>
                                    <small>Users</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-2">
                                <div class="stat-card text-center" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                    <i class="fas fa-user-graduate fa-2x mb-2"></i>
                                    <h4><?php echo count($branch_students); ?></h4>
                                    <small>Students</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-2">
                                <div class="stat-card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                    <i class="fas fa-question-circle fa-2x mb-2"></i>
                                    <h4><?php echo count($branch_enquiries); ?></h4>
                                    <small>Enquiries</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-2">
                                <div class="stat-card text-center" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                                    <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                    <h4><?php echo number_format($branch_expenses/1000, 0); ?>K</h4>
                                    <small>Expenses</small>
                                </div>
                            </div>
                        </div>

                        <!-- Users List -->
                        <div class="user-list">
                            <h6 class="mb-2"><i class="fas fa-users me-2"></i>Branch Users</h6>
                            <?php if (empty($branch_users)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-user-slash fa-2x mb-2"></i>
                                <p class="mb-0">No users found</p>
                            </div>
                            <?php else: ?>
                            <?php foreach ($branch_users as $user_data): ?>
                            <div class="user-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-circle fa-2x text-primary me-2"></i>
                                    <div>
                                        <strong><?php echo htmlspecialchars($user_data['username']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($user_data['first_name'] . ' ' . $user_data['last_name']); ?>
                                        </small>
                                        <br>
                                        <span class="badge bg-<?php echo $user_data['role'] === 'branch_manager' ? 'warning' : 'info'; ?> badge-sm">
                                            <?php echo ucfirst(str_replace('_', ' ', $user_data['role'])); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-warning" onclick="openPasswordModal(<?php echo $user_data['id']; ?>, '<?php echo htmlspecialchars($user_data['username']); ?>')" title="Change Password">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    <?php if ($user_data['id'] != $_SESSION['user_id']): ?>
                                    <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteUser(<?php echo $user_data['id']; ?>, '<?php echo htmlspecialchars($user_data['username']); ?>')" title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">
                                <i class="fas fa-user-tie me-1"></i>
                                <?php echo count(array_filter($branch_users, function($u) { return $u['role'] === 'branch_manager'; })); ?> Managers
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                <?php echo count(array_filter($branch_users, function($u) { return $u['role'] === 'branch_staff'; })); ?> Staff
                            </small>
                            <a href="branch_login.php?branch=<?php echo $branch_id; ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Create New User for <span id="modal_branch_name"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="create_user">
                    <input type="hidden" name="branch_id" id="create_branch_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="generatePassword()">
                                            <i class="fas fa-random"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password_icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="branch_staff">Branch Staff</option>
                                        <option value="branch_manager">Branch Manager</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="passwordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="update_password">
                    <input type="hidden" name="user_id" id="password_user_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">User:</label>
                            <input type="text" class="form-control" id="password_username" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="generateNewPassword()">
                                    <i class="fas fa-random"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye" id="new_password_icon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Update Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>Delete User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" id="delete_user_id">
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone!
                        </div>
                        <p>Are you sure you want to delete user <strong id="delete_username"></strong>?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openCreateUserModal(branchId, branchName) {
            document.getElementById('create_branch_id').value = branchId;
            document.getElementById('modal_branch_name').textContent = branchName;
            document.getElementById('username').value = '';
            document.getElementById('email').value = '';
            document.getElementById('password').value = 'password123';
            document.getElementById('first_name').value = '';
            document.getElementById('last_name').value = '';
            document.getElementById('role').value = 'branch_staff';
            document.getElementById('phone').value = '';
            
            new bootstrap.Modal(document.getElementById('createUserModal')).show();
        }

        function openPasswordModal(userId, username) {
            document.getElementById('password_user_id').value = userId;
            document.getElementById('password_username').value = username;
            document.getElementById('new_password').value = '';
            
            new bootstrap.Modal(document.getElementById('passwordModal')).show();
        }

        function confirmDeleteUser(userId, username) {
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_username').textContent = username;
            
            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }

        function generatePassword() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('password').value = password;
        }

        function generateNewPassword() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('new_password').value = password;
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + '_icon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>

