<?php
// Database Connection Test Script
// Use this to test if your database connection is working

echo "<h2>Database Connection Test</h2>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'expense_management';

echo "<p>Testing connection to: <strong>$host</strong></p>";
echo "<p>Username: <strong>$username</strong></p>";
echo "<p>Database: <strong>$database</strong></p>";

try {
    // Test connection without database
    echo "<h3>Step 1: Testing MySQL Connection</h3>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ MySQL connection successful</p>";
    
    // Test database exists
    echo "<h3>Step 2: Checking Database</h3>";
    $result = $pdo->query("SHOW DATABASES LIKE '$database'");
    if ($result->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Database '$database' exists</p>";
        
        // Test connection with database
        echo "<h3>Step 3: Testing Database Connection</h3>";
        $pdo->exec("USE `$database`");
        echo "<p style='color: green;'>✓ Connected to database '$database'</p>";
        
        // Check tables
        echo "<h3>Step 4: Checking Tables</h3>";
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ Found " . count($tables) . " tables:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠ No tables found in database</p>";
            echo "<p><a href='setup_database.php'>Run Database Setup</a></p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠ Database '$database' does not exist</p>";
        echo "<p><a href='setup_database.php'>Create Database</a></p>";
    }
    
    echo "<h3 style='color: green;'>✅ Connection test completed successfully!</h3>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ Connection failed:</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    
    echo "<h4>Common Solutions:</h4>";
    echo "<ul>";
    echo "<li><strong>MySQL not running:</strong> Start MySQL service</li>";
    echo "<li><strong>Wrong credentials:</strong> Check username/password in database_config.php</li>";
    echo "<li><strong>Permission denied:</strong> Make sure MySQL user has proper permissions</li>";
    echo "<li><strong>Port issues:</strong> MySQL might be running on different port</li>";
    echo "</ul>";
    
    echo "<h4>Quick Fixes:</h4>";
    echo "<ol>";
    echo "<li>Start MySQL: <code>brew services start mysql</code> (on Mac)</li>";
    echo "<li>Or: <code>sudo service mysql start</code> (on Linux)</li>";
    echo "<li>Test manually: <code>mysql -u root -p</code></li>";
    echo "</ol>";
}
?>