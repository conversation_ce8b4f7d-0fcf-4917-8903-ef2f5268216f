<?php
header('Content-Type: application/json');
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/User.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = $_POST['user_id'] ?? null;
    $new_password = $_POST['new_password'] ?? null;
    $new_name = $_POST['new_name'] ?? null;
    
    // Validate required fields
    if (!$user_id) {
        echo json_encode(['success' => false, 'message' => 'User ID is required']);
        exit;
    }
    
    try {
        $user = new User();
        
        // Check if user exists
        $existingUser = $user->getUserById($user_id);
        if (!$existingUser) {
            echo json_encode(['success' => false, 'message' => 'User not found']);
            exit;
        }
        
        // Build update query based on what's being updated
        $updates = [];
        $params = [];
        
        if (!empty($new_password)) {
            // Validate password strength
            if (strlen($new_password) < 6) {
                echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters long']);
                exit;
            }
            $updates[] = "password = ?";
            $params[] = password_hash($new_password, PASSWORD_DEFAULT);
        }
        
        if (!empty($new_name)) {
            $updates[] = "first_name = ?";
            $updates[] = "last_name = ?";
            $params[] = $new_name;
            $params[] = $new_name; // Use same name for both first and last
        }
        
        if (empty($updates)) {
            echo json_encode(['success' => false, 'message' => 'No changes to update']);
            exit;
        }
        
        $params[] = $user_id; // Add user ID for WHERE clause
        
        $query = "UPDATE users SET " . implode(", ", $updates) . " WHERE id = ?";
        $stmt = $user->conn->prepare($query);
        $result = $stmt->execute($params);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'User updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update user']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>