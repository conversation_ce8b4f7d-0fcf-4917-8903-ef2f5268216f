<?php
header('Content-Type: application/json');
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/User.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = new User();
    
    $username = $_POST['username'] ?? null;
    $email = $_POST['email'] ?? null;
    $password = $_POST['password'] ?? null;
    $role = $_POST['role'] ?? null;
    $branch_id = $_POST['branch_id'] ?? null;
    $first_name = $_POST['first_name'] ?? null;
    $last_name = $first_name; // Use first_name as last_name for simplicity
    $phone = $_POST['phone'] ?? null;
    
    
    // Validate required fields
    if (!$username || !$password || !$role || !$branch_id || !$first_name) {
        echo json_encode(['success' => false, 'message' => 'All required fields must be filled']);
        exit;
    }
    
    // Check if username already exists
    if ($user->usernameExists($username)) {
        echo json_encode(['success' => false, 'message' => 'Username already exists']);
        exit;
    }
    
    // Check if email already exists (only if email is provided)
    if (!empty($email) && $user->emailExists($email)) {
        echo json_encode(['success' => false, 'message' => 'Email already exists']);
        exit;
    }
    
    try {
        $result = $user->createUser($username, $email, $password, $role, $branch_id, $first_name, $last_name, $phone);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'User created successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to create user']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>


