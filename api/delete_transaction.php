<?php
header('Content-Type: application/json');
session_start();

require_once '../classes/Transaction.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $transaction = new Transaction();
    
    $transaction_id = $_GET['id'] ?? null;
    
    if (!$transaction_id) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
        exit;
    }
    
    try {
        $result = $transaction->deleteTransaction($transaction_id);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Transaction deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete transaction']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>


