<?php
header('Content-Type: application/json');
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/User.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = null;
    
    // Handle both JSON and form data
    $input = json_decode(file_get_contents('php://input'), true);
    if ($input) {
        $user_id = $input['id'] ?? null;
    } else {
        $user_id = $_POST['id'] ?? null;
    }
    
    // Validate required fields
    if (!$user_id) {
        echo json_encode(['success' => false, 'message' => 'User ID is required']);
        exit;
    }
    
    // Prevent admin from deleting themselves
    if ($user_id == $_SESSION['user_id']) {
        echo json_encode(['success' => false, 'message' => 'You cannot delete your own account']);
        exit;
    }
    
    try {
        $user = new User();
        
        // Check if user exists
        $existingUser = $user->getUserById($user_id);
        if (!$existingUser) {
            echo json_encode(['success' => false, 'message' => 'User not found']);
            exit;
        }
        
        // Delete user
        $query = "DELETE FROM users WHERE id = ?";
        $stmt = $user->conn->prepare($query);
        $result = $stmt->execute([$user_id]);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete user']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
