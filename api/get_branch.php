<?php
header('Content-Type: application/json');
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/Branch.php';

if (isset($_GET['id'])) {
    $branch = new Branch();
    
    try {
        $branchData = $branch->getBranchById($_GET['id']);
        
        if ($branchData) {
            echo json_encode(['success' => true, 'data' => $branchData]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Branch not found']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Branch ID is required']);
}
?>


