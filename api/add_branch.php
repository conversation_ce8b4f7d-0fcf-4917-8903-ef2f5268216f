<?php
header('Content-Type: application/json');
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/Branch.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $branch = new Branch();
    
    $name = $_POST['name'] ?? null;
    $location = $_POST['location'] ?? null;
    $manager_name = $_POST['manager_name'] ?? null;
    $phone = $_POST['phone'] ?? null;
    $email = $_POST['email'] ?? null;
    
    // Validate required fields
    if (!$name || !$location) {
        echo json_encode(['success' => false, 'message' => 'Branch name and location are required']);
        exit;
    }
    
    // Check if branch name already exists
    if ($branch->branchNameExists($name)) {
        echo json_encode(['success' => false, 'message' => 'Branch name already exists']);
        exit;
    }
    
    try {
        $result = $branch->createBranch($name, $location, $manager_name, $phone, $email);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Branch created successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to create branch']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>

