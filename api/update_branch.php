<?php
header('Content-Type: application/json');
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/Branch.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $branch = new Branch();
    
    $branch_id = $_POST['branch_id'] ?? null;
    $name = $_POST['name'] ?? null;
    $location = $_POST['location'] ?? null;
    $contact_person = $_POST['contact_person'] ?? null;
    $phone = $_POST['phone'] ?? null;
    $email = $_POST['email'] ?? null;
    
    // Validate required fields
    if (!$branch_id || !$name || !$location) {
        echo json_encode(['success' => false, 'message' => 'Branch ID, name and location are required']);
        exit;
    }
    
    // Check if branch name already exists (excluding current branch)
    if ($branch->branchNameExists($name, $branch_id)) {
        echo json_encode(['success' => false, 'message' => 'Branch name already exists']);
        exit;
    }
    
    try {
        $result = $branch->updateBranch($branch_id, $name, $location, $contact_person, $phone, $email);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Branch updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update branch']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>


