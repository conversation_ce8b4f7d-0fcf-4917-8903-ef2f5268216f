<?php
header('Content-Type: application/json');
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

require_once '../classes/Branch.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
    $branch = new Branch();
    
    try {
        $result = $branch->deleteBranch($_POST['id']);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Branch deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete branch']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}
?>


