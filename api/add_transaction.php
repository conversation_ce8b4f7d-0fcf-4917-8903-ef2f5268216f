<?php
header('Content-Type: application/json');
session_start();

require_once '../classes/Transaction.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'Please log in to add transactions']);
        exit;
    }
    
    $transaction = new Transaction();
    
    $branch_id = $_POST['branch_id'] ?? null;
    $amount = $_POST['amount'] ?? null;
    $type = $_POST['type'] ?? null;
    $category = $_POST['category'] ?? null;
    $date = $_POST['date'] ?? null;
    $created_by = $_SESSION['user_id'];
    
    // Convert empty branch_id to NULL for main transactions
    if ($branch_id === '' || $branch_id === '0') {
        $branch_id = null;
    }
    
    // Create description from category
    $description = $category;
    
    // Validate required fields - branch_id can be NULL for main transactions
    $missingFields = [];
    if (empty($amount) || $amount === '') $missingFields[] = 'amount';
    if (empty($type) || $type === '') $missingFields[] = 'type';
    if (empty($category) || $category === '') $missingFields[] = 'category';
    if (empty($date) || $date === '') $missingFields[] = 'date';
    
    if (!empty($missingFields)) {
        echo json_encode(['success' => false, 'message' => 'All required fields must be filled']);
        exit;
    }
    
    // Validate amount
    if (!is_numeric($amount) || $amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Amount must be a positive number']);
        exit;
    }
    
    // Permission validation based on user role
    $user_role = $_SESSION['role'] ?? '';
    $user_branch_id = $_SESSION['branch_id'] ?? null;
    
    if ($user_role === 'admin') {
        // Admin can add transactions to any branch (including main with branch_id = NULL)
        // No additional restrictions
    } elseif ($user_role === 'branch_manager' || $user_role === 'branch_staff') {
        // Branch users can only add transactions to their own branch
        // Convert user_branch_id to string for comparison if it's numeric
        $user_branch_id_str = (string)$user_branch_id;
        $branch_id_str = $branch_id === null ? 'null' : (string)$branch_id;
        
        if ($branch_id_str !== $user_branch_id_str) {
            echo json_encode(['success' => false, 'message' => 'You can only add transactions for your own branch']);
            exit;
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Insufficient permissions to add transactions']);
        exit;
    }
    
    try {
        $result = $transaction->addTransaction($branch_id, $amount, $type, $description, $category, $date, $created_by);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Transaction added successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add transaction']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
