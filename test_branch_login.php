<?php
require_once 'classes/Branch.php';

echo "<h2>Branch Login Test</h2>";

try {
    $branch = new Branch();
    
    // Test branch 3 (Kasaragod)
    echo "<h3>Testing Branch ID 3:</h3>";
    $branch_info = $branch->getBranchById(3);
    
    if ($branch_info) {
        echo "<p style='color: green;'>✅ Branch found:</p>";
        echo "<ul>";
        echo "<li>ID: " . $branch_info['id'] . "</li>";
        echo "<li>Name: " . $branch_info['name'] . "</li>";
        echo "<li>Location: " . $branch_info['location'] . "</li>";
        echo "</ul>";
        
        echo "<p><strong>Direct URL should work:</strong></p>";
        echo "<a href='branch_login.php?branch=3' target='_blank'>branch_login.php?branch=3</a>";
        
    } else {
        echo "<p style='color: red;'>❌ Branch ID 3 not found</p>";
    }
    
    echo "<hr>";
    echo "<h3>All Available Branches:</h3>";
    $all_branches = $branch->getAllBranches();
    
    if (empty($all_branches)) {
        echo "<p style='color: red;'>❌ No branches found in database</p>";
        echo "<p>Please run <a href='setup_database.php'>setup_database.php</a> first.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Name</th><th>Location</th><th>Login URL</th></tr>";
        
        foreach ($all_branches as $branch_data) {
            echo "<tr>";
            echo "<td>" . $branch_data['id'] . "</td>";
            echo "<td>" . $branch_data['name'] . "</td>";
            echo "<td>" . $branch_data['location'] . "</td>";
            echo "<td><a href='branch_login.php?branch=" . $branch_data['id'] . "' target='_blank'>branch_login.php?branch=" . $branch_data['id'] . "</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Database connection issue. Please check your database setup.</p>";
}

echo "<hr>";
echo "<h3>Test URLs:</h3>";
echo "<ul>";
echo "<li><a href='branch_login.php' target='_blank'>branch_login.php</a> - Branch selection page</li>";
echo "<li><a href='branch_login.php?branch=1' target='_blank'>branch_login.php?branch=1</a> - Uppala branch login</li>";
echo "<li><a href='branch_login.php?branch=2' target='_blank'>branch_login.php?branch=2</a> - Seethangoli branch login</li>";
echo "<li><a href='branch_login.php?branch=3' target='_blank'>branch_login.php?branch=3</a> - Kasaragod branch login</li>";
echo "<li><a href='branch_login.php?branch=4' target='_blank'>branch_login.php?branch=4</a> - Kochi branch login</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Quick Test:</h3>";
echo "<p>If branch 3 (Kasaragod) exists, you should see a login form when you click the link above.</p>";
echo "<p>Try logging in with:</p>";
echo "<ul>";
echo "<li><strong>Manager:</strong> kasaragod_manager / password123</li>";
echo "<li><strong>Staff:</strong> kasaragod_staff / password123</li>";
echo "</ul>";
?>
