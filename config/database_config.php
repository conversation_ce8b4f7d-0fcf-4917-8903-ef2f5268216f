<?php
// Local Database Configuration
// This file contains the localhost database credentials

// Database Configuration for Local Development
define('DB_HOST', 'localhost');
define('DB_NAME', 'expense_management');
define('DB_USER', 'root');
define('DB_PASS', '');

// Test database connection
function testDatabaseConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        echo "Database connection failed: " . $e->getMessage();
        return null;
    }
}
?>
