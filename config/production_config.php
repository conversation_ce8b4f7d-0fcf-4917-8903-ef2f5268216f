<?php
// Production Configuration for online.mycloudforge.com
// This file should be uploaded to your production server

// Base URL Configuration for Local Development
define('BASE_URL', 'http://localhost');
define('APP_URL', BASE_URL . '/new_expns_app');
define('APP_NAME', 'Expense Management System');

// Database Configuration for Local Development
define('DB_HOST', 'localhost');
define('DB_NAME', 'expense_management');
define('DB_USER', 'root');
define('DB_PASS', '');

// Security Configuration
define('ENCRYPTION_KEY', 'your-secure-encryption-key-change-this-in-production');
define('SESSION_TIMEOUT', 3600); // 1 hour
define('CSRF_TOKEN_LIFETIME', 3600);

// Backup Configuration
define('BACKUP_DIR', '/var/backups/expense_management/');
define('BACKUP_RETENTION_DAYS', 30);
define('AUTO_BACKUP_ENABLED', true);
define('BACKUP_SCHEDULE', 'daily');

// Email Configuration (Update with your SMTP settings)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', 'noreply@localhost');
define('FROM_NAME', 'Expense Management System');

// File Upload Configuration
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);

// Logging Configuration
define('LOG_LEVEL', 'INFO');
define('LOG_FILE', __DIR__ . '/../logs/app.log');
define('ERROR_LOG', __DIR__ . '/../logs/error.log');

// Cache Configuration
define('CACHE_ENABLED', true);
define('CACHE_DIR', __DIR__ . '/../cache/');
define('CACHE_LIFETIME', 3600);

// API Configuration
define('API_RATE_LIMIT', 1000); // requests per hour
define('API_TIMEOUT', 30);
define('API_KEY_REQUIRED', false);

// Performance Configuration
define('ENABLE_COMPRESSION', true);
define('ENABLE_CACHING', true);
define('MINIFY_CSS_JS', true);
define('OPCACHE_ENABLED', true);

// Monitoring Configuration
define('ENABLE_ANALYTICS', true);
define('ERROR_REPORTING', true);
define('PERFORMANCE_MONITORING', true);
define('HEALTH_CHECK_ENABLED', true);

// Mobile Configuration
define('MOBILE_OPTIMIZED', true);
define('PWA_ENABLED', true);
define('PWA_NAME', 'Expense Management');
define('PWA_SHORT_NAME', 'ExpenseApp');
define('PWA_THEME_COLOR', '#4e73df');

// SSL Configuration (Disabled for local development)
define('FORCE_HTTPS', false);
define('HSTS_ENABLED', false);
define('SECURE_COOKIES', false);

// Production Environment Settings
if (getEnvironment() === 'production') {
    // Disable error display in production
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', ERROR_LOG);
    
    // Set timezone
    date_default_timezone_set('Asia/Kolkata');
    
    // Enable compression
    if (ENABLE_COMPRESSION && extension_loaded('zlib')) {
        ob_start('ob_gzhandler');
    }
    
    // Security headers
    if (!headers_sent()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        
        if (HSTS_ENABLED) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }
}

// Get current environment
function getEnvironment() {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
        return 'development';
    } elseif (strpos($host, 'staging') !== false) {
        return 'staging';
    } else {
        return 'production';
    }
}

// Get base URL based on environment
function getBaseUrl() {
    return APP_URL;
}

// Get full URL for a path
function getFullUrl($path = '') {
    return APP_URL . '/' . ltrim($path, '/');
}

// Get asset URL
function getAssetUrl($asset) {
    return APP_URL . '/assets/' . ltrim($asset, '/');
}

// Database connection helper
function getDatabaseConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return null;
    }
}

// Logging helper
function writeLog($message, $level = 'INFO') {
    if (defined('LOG_FILE')) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
        file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
    }
}

// Security helper
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Performance monitoring
function startTimer() {
    return microtime(true);
}

function endTimer($start_time) {
    return microtime(true) - $start_time;
}

// Health check endpoint
if (isset($_GET['health']) && HEALTH_CHECK_ENABLED) {
    header('Content-Type: application/json');
    
    $health = [
        'status' => 'ok',
        'timestamp' => date('c'),
        'version' => '1.0.0',
        'environment' => getEnvironment(),
        'database' => getDatabaseConnection() ? 'connected' : 'disconnected',
        'memory_usage' => memory_get_usage(true),
        'uptime' => time() - $_SERVER['REQUEST_TIME']
    ];
    
    echo json_encode($health);
    exit;
}
?>
