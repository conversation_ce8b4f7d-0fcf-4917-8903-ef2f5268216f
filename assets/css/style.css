/* Premium Minimalistic CSS for Expense Management System */

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap');

:root {
    /* Modern Primary Colors - Indigo Palette */
    --primary-50: #eef2ff;
    --primary-100: #e0e7ff;
    --primary-200: #c7d2fe;
    --primary-300: #a5b4fc;
    --primary-400: #818cf8;
    --primary-500: #6366f1;
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-800: #3730a3;
    --primary-900: #312e81;
    
    /* Modern Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Modern Semantic Colors */
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;
    
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    
    --danger-50: #fdf2f8;
    --danger-500: #f43f5e;
    --danger-600: #e11d48;
    --danger-700: #be123c;
    
    --info-50: #f0f9ff;
    --info-500: #06b6d4;
    --info-600: #0891b2;
    --info-700: #0e7490;
    
    /* Modern Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.5;
    padding-bottom: 80px;
    min-height: 100vh;
    font-size: 0.875rem;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Professional Contained Layout */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

@media (min-width: 1200px) {
    .main-container {
        max-width: 1200px;
        padding: 0 3rem;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 0 1rem;
        max-width: none;
    }
}

/* Professional Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    color: var(--gray-900);
    letter-spacing: -0.015em;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

h1 { font-size: 1.875rem; }
h2 { font-size: 1.625rem; }
h3 { font-size: 1.375rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.9375rem; }

/* Professional Content Typography */
.content-text {
    font-size: 0.9375rem;
    line-height: 1.6;
    color: var(--gray-700);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1.25rem;
    letter-spacing: -0.01em;
}

.text-muted {
    color: var(--gray-500) !important;
    font-size: 0.875rem;
}

/* Global Icon Sizes */
.fa-xs { font-size: 0.625rem; }
.fa-sm { font-size: 0.75rem; }
.fa-lg { font-size: 1rem; }
.fa-xl { font-size: 1.25rem; }
.fa-2x { font-size: 1.5rem; }

/* Global Spacing */
.p-xs { padding: 0.25rem !important; }
.p-sm { padding: 0.5rem !important; }
.p-md { padding: 0.75rem !important; }
.p-lg { padding: 1rem !important; }

.m-xs { margin: 0.25rem !important; }
.m-sm { margin: 0.5rem !important; }
.m-md { margin: 0.75rem !important; }
.m-lg { margin: 1rem !important; }

/* Professional Spacing */
.section-spacing {
    margin-bottom: 2rem;
}

@media (min-width: 1200px) {
    .section-spacing {
        margin-bottom: 2.5rem;
    }
}

.card-spacing {
    margin-bottom: 1.5rem;
}

@media (min-width: 1200px) {
    .card-spacing {
        margin-bottom: 1.75rem;
    }
}

/* Modern Navigation - Compact */
.navbar {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    padding: 1rem 0;
    min-height: 70px;
}

/* Premium Brand Styling */
.premium-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: all 0.2s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin-right: 1rem;
    position: relative;
}

.premium-brand:hover {
    background: rgba(99, 102, 241, 0.05);
}

.premium-brand:hover .brand-main {
    color: var(--primary-600);
}


.brand-text {
    display: flex;
    align-items: center;
    line-height: 1;
}

.brand-main {
    color: var(--gray-900);
    font-weight: 600;
    font-size: 1.25rem;
    letter-spacing: -0.025em;
}

.brand-sub {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.04em;
}

/* Premium Dashboard Title */
.premium-dashboard-title {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.premium-dashboard-title:hover {
    background: rgba(34, 197, 94, 0.05);
}

.dashboard-title {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.title-main {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--gray-900);
    letter-spacing: -0.005em;
    margin-bottom: -1px;
}

.title-sub {
    font-size: 0.8125rem;
    font-weight: 400;
    color: var(--primary-600);
    text-transform: uppercase;
    letter-spacing: 0.06em;
}

.navbar-brand {
    font-weight: 700;
    color: var(--gray-900) !important;
    font-size: 1rem;
}

.navbar-nav .nav-link {
    color: var(--gray-500) !important;
    font-weight: 400;
    padding: 0.75rem 1rem !important;
    border-radius: 10px;
    margin: 0 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 48px;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--gray-700) !important;
    background: var(--gray-100);
}

.navbar-nav .nav-link i {
    font-size: 1rem;
    transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-600) !important;
    background: var(--primary-50);
}

.navbar-nav .nav-link.active {
    background: var(--primary-100);
    color: var(--primary-700) !important;
}

.navbar-nav .nav-link.active i {
    color: var(--primary-700) !important;
}

.navbar-nav .dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.navbar-nav .dropdown-item {
    border-radius: var(--radius);
    padding: 0.5rem 0.75rem;
    margin: 0.125rem 0;
    font-weight: 500;
    transition: all 0.2s ease;
}

.navbar-nav .dropdown-item:hover {
    background-color: var(--primary-50);
    color: var(--primary-600);
}

/* Admin Avatar Styling - Removed */

/* Modern Card Styles - Compact */
.card {
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    box-shadow: var(--shadow-xs);
    background: white;
    transition: all 0.2s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-sm);
}

.card-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.25rem;
    font-weight: 500;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 0.5rem 1rem;
}

/* Minimalistic Statistics Cards */
.stats-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 1.5rem;
    color: var(--gray-800);
    position: relative;
    height: 100%;
    box-shadow: none;
}

.stats-card:hover {
    border-color: var(--gray-300);
}

.stats-card .stats-icon {
    width: 24px;
    height: 24px;
    background: transparent;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--gray-500);
    font-weight: 600;
}

.stats-card .stats-value {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    line-height: 1.2;
    color: var(--gray-900);
}

.stats-card .stats-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    font-weight: 400;
    text-transform: none;
    letter-spacing: 0;
}

/* Minimalistic Color Variants for Stats Cards */
.stats-card.success .stats-icon {
    color: var(--success-600);
}

.stats-card.success .stats-value {
    color: var(--success-600);
}

.stats-card.warning .stats-icon {
    color: var(--warning-600);
}

.stats-card.warning .stats-value {
    color: var(--warning-600);
}

.stats-card.info .stats-icon {
    color: var(--info-600);
}

.stats-card.info .stats-value {
    color: var(--info-600);
}

.stats-card.danger .stats-icon {
    color: var(--danger-600);
}

.stats-card.danger .stats-value {
    color: var(--danger-600);
}

/* Clean Design - No Border Accents */

/* Modern Button Styles - Compact */
.btn {
    border-radius: var(--radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border: none;
    position: relative;
    overflow: hidden;
    font-size: 0.8rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: 8px;
    box-shadow: var(--shadow-xs);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--primary-600);
    box-shadow: var(--shadow-sm);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-500);
    color: var(--primary-600);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-700) 0%, var(--success-800) 100%);
    transform: translateY(-1px);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-700) 0%, var(--warning-800) 100%);
    transform: translateY(-1px);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-700) 0%, var(--danger-800) 100%);
    transform: translateY(-1px);
    color: white;
}

/* Text Colors - Updated */
.text-primary {
    color: var(--primary-600) !important;
}

.text-success {
    color: var(--success-600) !important;
}

.text-info {
    color: var(--info-600) !important;
}

.text-warning {
    color: var(--warning-600) !important;
}

.text-danger {
    color: var(--danger-600) !important;
}

/* Background Colors - Updated */
.bg-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%) !important;
}

/* Modern Form Styles - Compact */
.form-control, .form-select {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background: var(--gray-100);
    border: 2px solid var(--gray-200);
    border-right: none;
    color: var(--gray-600);
}

/* Modern Table Styles - Compact */
.table {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: none;
    border: 1px solid var(--gray-100);
}

.table th {
    background: transparent;
    border: none;
    font-weight: 500;
    color: var(--gray-600);
    padding: 0.875rem 0.875rem 0.75rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    border-bottom: 1px solid var(--gray-200);
}

.table td {
    border: none;
    border-bottom: 1px solid var(--gray-50);
    padding: 0.875rem;
    vertical-align: middle;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.table tbody tr {
    transition: all 0.15s ease;
}

.table tbody tr:hover {
    background: rgba(34, 197, 94, 0.02);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Important columns with light grey background */
.table .important-column {
    background: rgba(0, 0, 0, 0.02);
    font-weight: 500;
}

.table th.important-column {
    background: rgba(0, 0, 0, 0.03);
    font-weight: 600;
}

/* Specific column styling */
.table td:nth-child(2), /* Description column */
.table th:nth-child(2) {
    background: rgba(0, 0, 0, 0.015);
}

.table td:nth-child(4), /* Amount column */
.table th:nth-child(4) {
    background: rgba(0, 0, 0, 0.02);
    text-align: right;
}

/* Professional Transaction Type Indicators - Minimal */
.transaction-type {
    display: inline-block;
    font-size: 0.6875rem;
    font-weight: 500;
    padding: 0.1875rem 0.5rem;
    border-radius: 3px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.transaction-type.income {
    background: rgba(34, 197, 94, 0.06);
    color: var(--success-600);
}

.transaction-type.expense {
    background: rgba(239, 68, 68, 0.06);
    color: var(--danger-600);
}

/* Icons removed for cleaner design */

/* Professional Amount Styling */
.amount-income {
    color: var(--success-600);
    font-weight: 600;
}

.amount-expense {
    color: var(--danger-600);
    font-weight: 600;
}

.amount-neutral {
    color: var(--gray-700);
    font-weight: 600;
}

/* Modern Badge Styles - Compact */
.badge {
    font-size: 0.65rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.bg-info {
    background: var(--gray-100) !important;
    color: var(--gray-700) !important;
    border: 1px solid var(--gray-200);
}

.bg-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%) !important;
}

/* Mobile Bottom Navigation - Compact */
.navbar.fixed-bottom {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--primary-200);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    padding: 0.25rem 0;
    min-height: 60px;
}

.navbar.fixed-bottom .nav-link {
    color: var(--gray-500);
    padding: 0.25rem 0;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: var(--radius);
    margin: 0 0.125rem;
}

.navbar.fixed-bottom .nav-link.active {
    color: var(--primary-600);
    background: var(--primary-50);
}

.navbar.fixed-bottom .nav-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
}

.navbar.fixed-bottom .nav-link i {
    font-size: 1rem;
    margin-bottom: 0.125rem;
    display: block;
}

.navbar.fixed-bottom .nav-link small {
    font-size: 0.6rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

/* Progress Bar Styles */
.progress {
    height: 8px;
    border-radius: var(--radius);
    background: var(--gray-200);
    overflow: hidden;
}

.progress-bar {
    border-radius: var(--radius);
    transition: width 0.3s ease;
}

.progress-bar.bg-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%) !important;
}

.progress-bar.bg-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%) !important;
}

.progress-bar.bg-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%) !important;
}

/* Branch Cards - Premium */
.branch-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid var(--gray-200);
}

.branch-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Premium Alert Styles */
.alert {
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: linear-gradient(135deg, var(--success-50) 0%, var(--success-100) 100%);
    color: var(--success-700);
    border-left: 4px solid var(--success-500);
}

.alert-danger {
    background: linear-gradient(135deg, var(--danger-50) 0%, var(--danger-100) 100%);
    color: var(--danger-700);
    border-left: 4px solid var(--danger-500);
}

.alert-warning {
    background: linear-gradient(135deg, var(--warning-50) 0%, var(--warning-100) 100%);
    color: var(--warning-700);
    border-left: 4px solid var(--warning-500);
}

.alert-info {
    background: linear-gradient(135deg, var(--info-50) 0%, var(--info-100) 100%);
    color: var(--info-700);
    border-left: 4px solid var(--info-500);
}

/* Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}


/* Clean Design - No Animations */

/* Responsive Design - Compact */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
    
    .stats-card .stats-value {
        font-size: 1.5rem;
    }
    
    .stats-card .stats-icon {
        width: 20px;
        height: 20px;
        font-size: 0.6875rem;
        margin-bottom: 0.5rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }
    
    h1 { font-size: 1.25rem; }
    h2 { font-size: 1.125rem; }
    h3 { font-size: 1rem; }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .card-header {
        padding: 0.5rem 0.75rem;
    }
    
    .card-body {
        padding: 0.5rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
    
    .stats-card .stats-value {
        font-size: 1.5rem;
    }
    
    .stats-card .stats-icon {
        width: 20px;
        height: 20px;
        font-size: 0.6875rem;
        margin-bottom: 0.5rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8125rem;
    }
    
    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8125rem;
    }
    
    .navbar.fixed-bottom .nav-link {
        padding: 0.25rem 0;
    }
    
    .navbar.fixed-bottom .nav-link i {
        font-size: 0.875rem;
    }
    
    .navbar.fixed-bottom .nav-link small {
        font-size: 0.55rem;
    }
    
    /* Mobile Premium Brand */
    .brand-icon {
        width: 32px;
        height: 32px;
        margin-right: 0.5rem;
    }
    
    .brand-icon i {
        font-size: 0.9rem;
    }
    
    .brand-main {
        font-size: 1.125rem;
    }
    
    .brand-sub {
        font-size: 0.75rem;
    }
    
    .title-main {
        font-size: 1.125rem;
    }
    
    .title-sub {
        font-size: 0.75rem;
    }
    
    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.375rem; }
    h3 { font-size: 1.25rem; }
    
    /* Mobile Navigation */
    .navbar-nav .nav-link {
        width: 44px;
        height: 44px;
        margin: 0 0.25rem;
    }
    
    .navbar-nav .nav-link i {
        font-size: 1rem;
    }
    
    /* Admin Avatar Styling - Removed */
    
    /* Hide navbar toggler on mobile */
    .navbar-toggler {
        display: none !important;
    }
    
    /* Mobile Stats Grid */
    .stats-card {
        padding: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .stats-card .stats-icon {
        width: 20px;
        height: 20px;
        font-size: 0.6875rem;
        margin-bottom: 0.5rem;
    }
    
    .stats-card .stats-value {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }
    
    .stats-card .stats-label {
        font-size: 0.75rem;
    }
    
    /* Mobile Brand */
    .brand-main {
        font-size: 1.375rem;
    }
    
    .premium-brand {
        padding: 0.5rem 1rem;
    }
}

/* Floating Action Button */
.fab-container {
    position: fixed;
    bottom: 90px;
    right: 30px;
    z-index: 1000;
}

.fab-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: white;
    border: 1px solid var(--gray-200);
    color: var(--gray-600);
    font-size: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    position: relative;
}

.fab-btn:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-300);
    color: var(--primary-600);
    background: var(--primary-50);
    text-decoration: none;
}

.fab-btn:active,
.fab-btn:focus {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    color: var(--primary-700);
    text-decoration: none;
    outline: none;
}

/* Mobile FAB adjustments */
@media (max-width: 768px) {
    .fab-container {
        bottom: 90px;
        right: 15px;
    }
    
    .fab-btn {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }
}

/* Notification Container */
#alert-container {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    max-width: 400px !important;
}

#alert-container .alert {
    margin-bottom: 10px;
    animation: slideInRight 0.3s ease-out;
}

/* Mobile notification adjustments */
@media (max-width: 768px) {
    #alert-container {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
    }
    
    #alert-container .alert {
        font-size: 0.875rem;
        padding: 0.75rem;
    }
}

/* Utility Classes */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 600;
}

.font-weight-normal {
    font-weight: 400;
}

.text-uppercase {
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.text-gray-800 {
    color: var(--gray-800) !important;
}

.text-gray-300 {
    color: var(--gray-300) !important;
}

/* Additional Premium Effects */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Button Press Animation */
.btn:active {
    transform: scale(0.98) !important;
}

/* Loading Animation */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Floating Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Slide In Animation */
@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromTop {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    0% {
        transform: translateY(100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
}

.slide-in-top {
    animation: slideInFromTop 0.6s ease-out;
}

.slide-in-bottom {
    animation: slideInFromBottom 0.6s ease-out;
}

/* Scale Animation */
@keyframes scaleIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

/* Stagger Animation */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}
