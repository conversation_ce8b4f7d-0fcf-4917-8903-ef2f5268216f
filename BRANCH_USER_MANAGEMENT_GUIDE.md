# 👥 Branch User Management System

## Overview
Complete branch-specific user management system where each branch can independently manage their own users - create, edit, delete, and reset passwords.

## 🎯 Features

### ✅ **Branch User Management Features**
- **Create New Users**: Add branch staff and managers
- **Edit User Information**: Update names, emails, roles, phone numbers
- **Change Passwords**: Reset passwords for any branch user
- **Delete Users**: Remove users from the branch
- **User Statistics**: View branch user counts and roles
- **Role Management**: Assign branch_manager or branch_staff roles

### ✅ **Access Control**
- **Branch Managers**: Full user management access
- **Branch Staff**: View-only access (no user management)
- **Admin Users**: Can manage users across all branches

## 🚀 How to Access

### **From Branch Dashboard**
1. Login to your branch using `branch_login.php`
2. Go to your branch dashboard
3. Click the "User Management" tab
4. Or click "Full User Management" for advanced features

### **Direct Access**
- **URL**: `branch_user_management.php`
- **Requirements**: Must be logged in as branch_manager or admin

## 👥 User Roles & Permissions

### **Branch Manager** (`branch_manager`)
- ✅ Create new users
- ✅ Edit user information
- ✅ Change user passwords
- ✅ Delete users (except self)
- ✅ View all branch users
- ✅ Manage user roles

### **Branch Staff** (`branch_staff`)
- ❌ No user management access
- ✅ View own profile only
- ✅ Access branch dashboard

### **Admin** (`admin`)
- ✅ Full system access
- ✅ Manage users across all branches
- ✅ Create/delete branches
- ✅ System-wide management

## 🔧 User Management Operations

### **1. Create New User**
```
1. Click "Add New User" button
2. Fill in user details:
   - Username (unique)
   - Email
   - Password (auto-generate available)
   - First Name & Last Name
   - Role (Staff or Manager)
   - Phone (optional)
3. Click "Create User"
```

### **2. Edit User**
```
1. Click "Edit" button next to user
2. Modify user information
3. Click "Update User"
```

### **3. Change Password**
```
1. Click "Change Password" button
2. Enter new password (auto-generate available)
3. Click "Update Password"
```

### **4. Delete User**
```
1. Click "Delete" button next to user
2. Confirm deletion
3. User is permanently removed
```

## 📊 Branch User Interface

### **Main Dashboard Tab**
- Quick overview of branch users
- User statistics (managers vs staff)
- Quick actions (edit, password, delete)
- Link to full management interface

### **Full Management Interface**
- Complete user list with all details
- Advanced user creation form
- Detailed user editing
- Bulk operations
- User export functionality

## 🏢 Branch-Specific Features

### **Automatic Branch Assignment**
- All users created are automatically assigned to the current branch
- Users can only see and manage users from their own branch
- Branch isolation ensures data security

### **Branch Information Display**
- Shows current branch name and location
- Displays user counts by role
- Branch-specific statistics

## 🔐 Security Features

### **Self-Protection**
- Users cannot delete their own accounts
- Users cannot change their own roles (prevents privilege escalation)
- Session-based access control

### **Data Isolation**
- Branch users can only see their branch data
- No cross-branch user access
- Secure password handling

## 📱 User Interface Features

### **Responsive Design**
- Mobile-friendly interface
- Touch-optimized buttons
- Responsive tables and forms

### **User Experience**
- Auto-dismissing alerts
- Password generation tools
- Real-time form validation
- Intuitive navigation

## 🎨 Visual Elements

### **User Avatars**
- Font Awesome user icons
- Role-based color coding
- Status indicators

### **Role Badges**
- **Branch Manager**: Yellow badge
- **Branch Staff**: Blue badge
- **Admin**: Red badge

### **Status Indicators**
- **Active**: Green badge
- **Inactive**: Gray badge

## 🔄 Workflow Examples

### **Adding a New Staff Member**
1. Branch Manager logs into branch dashboard
2. Goes to User Management tab
3. Clicks "Add New User"
4. Fills in staff details:
   - Username: `uppala_staff2`
   - Email: `<EMAIL>`
   - Password: `password123` (or generate)
   - Name: `John Doe`
   - Role: `Branch Staff`
5. Clicks "Create User"
6. New staff member can now login to branch

### **Changing a User's Password**
1. Manager clicks "Change Password" next to user
2. Enters new password or generates one
3. Clicks "Update Password"
4. User receives new password and can login

### **Promoting Staff to Manager**
1. Manager clicks "Edit" next to staff member
2. Changes role from "Branch Staff" to "Branch Manager"
3. Clicks "Update User"
4. User now has manager privileges

## 🚨 Important Notes

### **User Creation Guidelines**
- Usernames must be unique across the entire system
- Passwords are hashed securely
- Email addresses should be valid
- Phone numbers are optional

### **Role Management**
- Only branch managers can create other managers
- Branch staff cannot be promoted to manager by themselves
- Admin users can manage all branches

### **Data Safety**
- User deletions are permanent
- Password changes take effect immediately
- All actions are logged in session

## 🔧 Technical Details

### **Files Created/Modified**
- `branch_user_management.php` - Main user management interface
- `branch_dashboard.php` - Added user management tab
- Enhanced user management with full CRUD operations

### **Database Operations**
- User creation with branch assignment
- User updates with validation
- Password hashing and updates
- User deletion with safety checks

## 🎯 Quick Start

### **For Branch Managers:**
1. Login to your branch: `branch_login.php`
2. Select your branch and enter credentials
3. Go to "User Management" tab in dashboard
4. Start creating and managing users

### **For System Administrators:**
1. Use admin login: `login.php`
2. Access full system management
3. Can manage users across all branches
4. Full system control

## 📞 Support

### **Troubleshooting**
- **Can't see User Management tab**: Check if you're logged in as branch_manager or admin
- **Can't create users**: Ensure you have branch_manager role
- **Password issues**: Use the password generator or contact admin

### **Common Issues**
- **Username already exists**: Choose a different username
- **Permission denied**: Contact admin to check your role
- **User not found**: Refresh the page or check user status

---

**🎉 The branch user management system is now fully functional! Each branch can independently manage their users with complete control over user creation, editing, and deletion.**
