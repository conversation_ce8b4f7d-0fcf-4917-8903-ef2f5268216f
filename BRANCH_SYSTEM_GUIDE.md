# 🌿 KFT Branch Management System

## Overview
Complete branch-specific login and management system for K<PERSON> (Karate Fitness Training) with individual branch dashboards and user management.

## 🚀 Quick Setup

### 1. Complete Setup (Recommended)
Visit: `http://your-domain/complete_branch_setup.php`
- Sets up database
- Creates admin user
- Creates branch users
- Shows all credentials

### 2. Manual Setup
1. Run `setup_database.php` (if not done)
2. Run `create_admin.php` (creates admin user)
3. Run `setup_branch_users.php` (creates branch users)

## 🔐 Login System

### Admin Access
- **URL**: `http://your-domain/login.php`
- **Username**: `admin`
- **Password**: `admin123`
- **Access**: Full system management

### Branch Access
- **URL**: `http://your-domain/branch_login.php`
- **Process**: Select branch → Enter credentials → Auto-redirect to branch dashboard

## 👥 Branch Users

Each branch has 2 user types:

### Branch Manager
- **Username Format**: `[branch]_manager`
- **Examples**: `uppala_manager`, `kochi_manager`
- **Password**: `password123`
- **Access**: Full branch management

### Branch Staff
- **Username Format**: `[branch]_staff`
- **Examples**: `uppala_staff`, `kochi_staff`
- **Password**: `password123`
- **Access**: Branch operations

## 🏢 Branches

1. **Uppala Branch**
   - Manager: `uppala_manager`
   - Staff: `uppala_staff`

2. **Seethangoli Branch**
   - Manager: `seethangoli_manager`
   - Staff: `seethangoli_staff`

3. **Kasaragod Branch**
   - Manager: `kasaragod_manager`
   - Staff: `kasaragod_staff`

4. **Kochi Branch**
   - Manager: `kochi_manager`
   - Staff: `kochi_staff`

## 🔄 User Flow

### For Branch Users
1. Go to Branch Login page
2. Select their branch
3. Enter username/password
4. Automatically redirected to branch dashboard
5. Can only see their branch data

### For Admin Users
1. Go to Admin Login page
2. Enter admin credentials
3. Redirected to main dashboard
4. Can see all branches and manage users

## 📊 Branch Dashboard Features

Each branch dashboard includes:
- **Student Management**: Add, view, edit students
- **Attendance System**: Mark and track attendance
- **Enquiry Management**: Handle new enquiries
- **Expense Tracking**: Branch-specific expenses
- **Performance Metrics**: Branch statistics

## 🛠️ System Features

### Branch Management (Admin Only)
- Add new branches
- Edit branch information
- Manage branch users
- View all branch data

### User Management
- Create branch users
- Reset passwords
- Manage user roles
- View user statistics

### Security Features
- Role-based access control
- Branch-specific data isolation
- Secure password hashing
- Session management

## 🔧 Technical Details

### Files Created/Modified
- `branch_login.php` - Branch-specific login system
- `branch_dashboard.php` - Individual branch dashboards
- `setup_branch_users.php` - Creates branch users
- `complete_branch_setup.php` - Complete setup script
- `login.php` - Updated for branch routing
- `branches.php` - Updated with role checks

### Database Structure
- **users table**: Stores all user credentials and roles
- **branches table**: Stores branch information
- **Role system**: admin, branch_manager, branch_staff

## 🎯 Usage Examples

### Login as Uppala Manager
1. Go to `branch_login.php`
2. Click "Uppala" branch
3. Username: `uppala_manager`
4. Password: `password123`
5. Redirected to Uppala branch dashboard

### Login as Admin
1. Go to `login.php`
2. Username: `admin`
3. Password: `admin123`
4. Access full system management

## 🚨 Troubleshooting

### Branch Login Not Working
1. Check if branch users exist: `check_admin.php`
2. Create branch users: `setup_branch_users.php`
3. Verify database setup: `setup_database.php`

### Admin Access Issues
1. Create admin user: `create_admin.php`
2. Check user status: `check_admin.php`
3. Verify database connection

### Branch Management Not Visible
- Only admin users can see branch management
- Login with admin credentials
- Check user role in session

## 📱 Mobile Support
- Responsive design for all devices
- Mobile-friendly navigation
- Touch-optimized interface

## 🔄 Future Enhancements
- Branch-specific notifications
- Advanced reporting per branch
- Branch performance analytics
- Multi-language support
- Advanced user permissions

---

**Need Help?** Check the setup scripts or contact system administrator.
