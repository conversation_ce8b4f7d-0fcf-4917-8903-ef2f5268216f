#!/bin/bash

# Production Deployment Script for Expense Management System
# Base URL: https://online.mycloudforge.com

echo "🚀 Starting Production Deployment..."

# Configuration
BASE_URL="https://online.mycloudforge.com"
APP_DIR="/var/www/html/new_expns_app"
BACKUP_DIR="/var/backups/expense_management"
DB_NAME="expense_management"
DB_USER="root"
DB_PASS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run as root (use sudo)"
    exit 1
fi

print_status "Creating backup directories..."
mkdir -p $BACKUP_DIR
mkdir -p $APP_DIR/backups
mkdir -p $APP_DIR/logs
mkdir -p $APP_DIR/cache

print_status "Setting up database backup..."
# Create database backup before deployment
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database_backup_$(date +%Y%m%d_%H%M%S).sql

if [ $? -eq 0 ]; then
    print_success "Database backup created successfully"
else
    print_warning "Database backup failed, continuing with deployment..."
fi

print_status "Setting file permissions..."
# Set proper permissions
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR
chmod -R 777 $APP_DIR/backups
chmod -R 777 $APP_DIR/logs
chmod -R 777 $APP_DIR/cache

print_status "Configuring Apache virtual host..."
# Create Apache virtual host configuration
cat > /etc/apache2/sites-available/expense-management.conf << EOF
<VirtualHost *:80>
    ServerName online.mycloudforge.com
    DocumentRoot $APP_DIR
    
    <Directory $APP_DIR>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Enable compression
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # Cache static files
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </LocationMatch>
    
    ErrorLog \${APACHE_LOG_DIR}/expense_management_error.log
    CustomLog \${APACHE_LOG_DIR}/expense_management_access.log combined
</VirtualHost>
EOF

# Enable the site
a2ensite expense-management.conf
a2enmod rewrite
a2enmod headers
a2enmod deflate
a2enmod expires

print_status "Configuring PHP settings..."
# Optimize PHP for production
cat > /etc/php/8.1/apache2/conf.d/99-expense-management.ini << EOF
; Production PHP settings for Expense Management System
memory_limit = 256M
max_execution_time = 60
max_input_time = 60
post_max_size = 20M
upload_max_filesize = 20M
max_file_uploads = 20

; Security settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Error reporting
display_errors = Off
log_errors = On
error_log = $APP_DIR/logs/php_errors.log

; Session settings
session.cookie_httponly = On
session.cookie_secure = On
session.use_strict_mode = On

; OPcache settings
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
EOF

print_status "Setting up SSL certificate..."
# Install Let's Encrypt SSL certificate
apt-get update
apt-get install -y certbot python3-certbot-apache

# Get SSL certificate
certbot --apache -d online.mycloudforge.com --non-interactive --agree-tos --email <EMAIL>

print_status "Configuring firewall..."
# Configure UFW firewall
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

print_status "Setting up automated backups..."
# Create backup script
cat > /usr/local/bin/backup_expense_management.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/expense_management"
APP_DIR="/var/www/html/new_expns_app"

# Database backup
mysqldump -u root expense_management > $BACKUP_DIR/database_$DATE.sql

# Application backup
tar -czf $BACKUP_DIR/application_$DATE.tar.gz -C $APP_DIR .

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /usr/local/bin/backup_expense_management.sh

# Add to crontab for daily backups
echo "0 2 * * * /usr/local/bin/backup_expense_management.sh" | crontab -

print_status "Setting up monitoring..."
# Create monitoring script
cat > /usr/local/bin/monitor_expense_management.sh << 'EOF'
#!/bin/bash
APP_DIR="/var/www/html/new_expns_app"
LOG_FILE="$APP_DIR/logs/monitor.log"

# Check if application is running
if ! curl -f http://localhost/new_expns_app/ > /dev/null 2>&1; then
    echo "$(date): Application is down!" >> $LOG_FILE
    # Send notification email (configure as needed)
    # echo "Expense Management System is down" | mail -s "Alert" <EMAIL>
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is high: ${DISK_USAGE}%" >> $LOG_FILE
fi

# Check database connection
if ! mysql -u root -e "SELECT 1" > /dev/null 2>&1; then
    echo "$(date): Database connection failed!" >> $LOG_FILE
fi
EOF

chmod +x /usr/local/bin/monitor_expense_management.sh

# Add to crontab for monitoring (every 5 minutes)
echo "*/5 * * * * /usr/local/bin/monitor_expense_management.sh" | crontab -

print_status "Restarting services..."
systemctl restart apache2
systemctl restart mysql

print_status "Running final checks..."
# Test database connection
if mysql -u $DB_USER -p$DB_PASS -e "USE $DB_NAME; SELECT 1;" > /dev/null 2>&1; then
    print_success "Database connection successful"
else
    print_error "Database connection failed"
fi

# Test web server
if curl -f http://localhost/new_expns_app/ > /dev/null 2>&1; then
    print_success "Web server is responding"
else
    print_warning "Web server test failed"
fi

print_success "Production deployment completed!"
print_status "Application URL: https://online.mycloudforge.com/new_expns_app"
print_status "Backup directory: $BACKUP_DIR"
print_status "Log directory: $APP_DIR/logs"

echo ""
echo "🔧 Post-deployment checklist:"
echo "1. Update database credentials in config/production.php"
echo "2. Configure email settings for notifications"
echo "3. Set up SSL certificate renewal"
echo "4. Test all application features"
echo "5. Configure monitoring alerts"
echo ""
echo "📊 Monitoring commands:"
echo "- Check logs: tail -f $APP_DIR/logs/php_errors.log"
echo "- Check backups: ls -la $BACKUP_DIR"
echo "- Check system status: systemctl status apache2 mysql"
echo ""
echo "🚀 Your Expense Management System is now live at:"
echo "   https://online.mycloudforge.com/new_expns_app"


