# 🏢 All Branches Dashboard Guide

## Overview
The All Branches Dashboard provides a comprehensive view of all branches with complete user management capabilities in one unified interface.

## 🎯 Key Features

### ✅ **Unified Branch View**
- **All Branches in One Place**: View all branches with their statistics
- **Real-time Data**: See users, students, enquiries, and expenses for each branch
- **User Management**: Create, edit passwords, and delete users for any branch
- **Quick Actions**: Fast access to user management functions

### ✅ **Per-Branch Statistics**
Each branch card shows:
- 👥 **Total Users**: Number of users in the branch
- 🎓 **Students**: Total enrolled students
- ❓ **Enquiries**: Active enquiries count
- 💰 **Expenses**: Total branch expenses

### ✅ **User Management Features**
- **Create Users**: Add new staff or managers to any branch
- **Change Passwords**: Reset passwords for any user
- **Delete Users**: Remove users from branches (with safety checks)
- **Role Assignment**: Set users as Branch Manager or Branch Staff

## 🚀 How to Access

**URL**: `all_branches_dashboard.php`

**From Main Dashboard**: Click on "All Branches" in navigation

**Requirements**: Must be logged in (any user with access can view)

## 📊 Dashboard Layout

### **Branch Cards**
Each branch is displayed as a card containing:

1. **Header Section**
   - Branch name and location
   - "Add User" button for quick user creation

2. **Statistics Section**
   - 4 colorful stat cards showing key metrics
   - Visual indicators for quick assessment

3. **Users List**
   - Scrollable list of all branch users
   - User details: username, name, role
   - Quick action buttons (password, delete)

4. **Footer Section**
   - Manager and staff counts
   - "Login" button to access branch directly

## 🔧 User Management Operations

### **1. Create New User**
```
1. Click "Add User" button on any branch card
2. Fill in user details:
   - Username (must be unique)
   - Email
   - Password (can auto-generate)
   - First & Last Name
   - Role (Manager or Staff)
   - Phone (optional)
3. Click "Create User"
4. User is immediately added to the branch
```

### **2. Change User Password**
```
1. Click the key icon next to any user
2. Enter new password (or auto-generate)
3. Click "Update Password"
4. Password is immediately changed
```

### **3. Delete User**
```
1. Click the trash icon next to any user
2. Confirm deletion in modal
3. Click "Delete User"
4. User is permanently removed
```

## 🎨 Design Features

### **Visual Consistency**
- Matches overall dashboard theme
- Uses same color scheme and styling
- Consistent with KFT Manager brand

### **Color-Coded Statistics**
- **Users**: Purple gradient
- **Students**: Pink gradient
- **Enquiries**: Blue gradient
- **Expenses**: Green gradient

### **Role Badges**
- **Branch Manager**: Yellow badge
- **Branch Staff**: Blue badge

### **Interactive Elements**
- Hover effects on cards
- Smooth transitions
- Responsive button groups
- Auto-dismissing alerts

## 📱 Responsive Design

### **Desktop View**
- 2 branches per row
- Full statistics display
- Complete user lists
- All features accessible

### **Tablet View**
- 1-2 branches per row
- Adjusted stat cards
- Scrollable user lists
- Touch-friendly buttons

### **Mobile View**
- 1 branch per column
- Stacked statistics
- Compact user display
- Mobile-optimized interface

## 🔐 Security Features

### **Access Control**
- View all branches (any authenticated user)
- Create/edit/delete users (based on permissions)
- Cannot delete your own account
- Secure password handling

### **Data Protection**
- Passwords are hashed
- Session-based authentication
- Input validation
- SQL injection prevention

## ⚡ Quick Actions

### **Password Management**
- Auto-generate secure passwords
- Toggle password visibility
- Copy password to clipboard (in forms)

### **User Creation**
- Pre-filled default password (password123)
- Auto-username suggestion
- Quick role selection

### **Bulk Operations**
- View all users across branches
- Quick access to each branch
- Fast user management

## 🎯 Use Cases

### **For System Administrators**
- Manage all branches from one interface
- Quick user creation across branches
- Monitor branch statistics
- Reset passwords system-wide

### **For Branch Managers**
- View their own branch details
- Manage branch users
- Access branch statistics
- Quick login to branch

### **For Staff Members**
- View branch information
- See team members
- Access branch resources

## 📊 Statistics Overview

### **Branch Metrics Displayed**
1. **User Count**: Total users in branch
2. **Student Count**: Active students enrolled
3. **Enquiry Count**: Pending/active enquiries
4. **Expense Total**: Total branch expenses (in thousands)

### **Real-time Updates**
- Data refreshes on page load
- Immediate updates after user operations
- Live statistics display

## 🚨 Important Notes

### **User Creation**
- Usernames must be unique across entire system
- Default password: password123 (should be changed)
- Email addresses should be valid
- Roles determine access permissions

### **Password Management**
- Passwords are encrypted
- Cannot view existing passwords
- Can only reset to new password
- Generated passwords are secure

### **User Deletion**
- Permanent action (cannot be undone)
- Cannot delete your own account
- Removes user from system completely
- All user data is marked inactive

## 🔄 Workflow Examples

### **Adding Multiple Users Across Branches**
1. Open All Branches Dashboard
2. For each branch:
   - Click "Add User"
   - Fill in details
   - Create user
3. All users are created and ready to login

### **Password Reset Campaign**
1. Go to All Branches Dashboard
2. For each user needing reset:
   - Click password icon
   - Generate new password
   - Save and communicate to user

### **Branch Audit**
1. View All Branches Dashboard
2. Check each branch:
   - Review user list
   - Verify roles
   - Check statistics
3. Make necessary adjustments

## 💡 Pro Tips

1. **Use Auto-Generate**: Let the system create secure passwords
2. **Consistent Naming**: Use format like `[branch]_[role]_[name]`
3. **Regular Reviews**: Check branch users periodically
4. **Update Passwords**: Change default passwords immediately
5. **Role Assignment**: Assign managers carefully for security

## 🎉 Benefits

### **Efficiency**
- Manage all branches from one page
- No need to switch between branch dashboards
- Quick user operations
- Batch management capabilities

### **Visibility**
- See all branches at once
- Compare branch statistics
- Monitor user distribution
- Track branch growth

### **Control**
- Centralized user management
- Consistent role assignment
- System-wide password policies
- Unified access control

---

**🚀 The All Branches Dashboard is your central hub for managing the entire KFT branch system!**
