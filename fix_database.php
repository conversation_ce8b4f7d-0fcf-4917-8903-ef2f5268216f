<?php
// Simple script to fix the database schema
// Run this file once to add missing columns

require_once 'config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Database Fix Script</h2>";
    echo "<p>Checking and fixing database schema...</p>";
    
    // Check if type column exists
    $checkType = $conn->query("SHOW COLUMNS FROM transactions LIKE 'type'");
    if ($checkType->rowCount() == 0) {
        echo "<p>Adding 'type' column to transactions table...</p>";
        $conn->exec("ALTER TABLE transactions ADD COLUMN type ENUM('income', 'expense') NOT NULL DEFAULT 'expense'");
        echo "<p style='color: green;'>✓ Type column added successfully</p>";
    } else {
        echo "<p style='color: blue;'>✓ Type column already exists</p>";
    }
    
    // Check if created_by column exists
    $checkCreatedBy = $conn->query("SHOW COLUMNS FROM transactions LIKE 'created_by'");
    if ($checkCreatedBy->rowCount() == 0) {
        echo "<p>Adding 'created_by' column to transactions table...</p>";
        $conn->exec("ALTER TABLE transactions ADD COLUMN created_by INT DEFAULT 1");
        echo "<p style='color: green;'>✓ Created_by column added successfully</p>";
    } else {
        echo "<p style='color: blue;'>✓ Created_by column already exists</p>";
    }
    
    // Update existing transactions to be 'expense' type
    $updateResult = $conn->exec("UPDATE transactions SET type = 'expense' WHERE type = 'expense' OR type IS NULL");
    echo "<p style='color: green;'>✓ Updated existing transactions</p>";
    
    // Add sample income transactions if they don't exist
    $checkIncome = $conn->query("SELECT COUNT(*) as count FROM transactions WHERE type = 'income'");
    $incomeCount = $checkIncome->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($incomeCount == 0) {
        echo "<p>Adding sample income transactions...</p>";
        $sampleIncome = [
            [1, 15000.00, 'income', 'Monthly fees collection', 'Fees', '2024-01-23', 1],
            [2, 8000.00, 'income', 'Special training fees', 'Special Training Fees', '2024-01-24', 1],
            [3, 12000.00, 'income', 'School training fees', 'School Training', '2024-01-25', 1],
            [4, 6000.00, 'income', 'Community training fees', 'Community Training', '2024-01-26', 1]
        ];
        
        $stmt = $conn->prepare("INSERT INTO transactions (branch_id, amount, type, description, category, date, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sampleIncome as $transaction) {
            $stmt->execute($transaction);
        }
        
        echo "<p style='color: green;'>✓ Sample income transactions added</p>";
    } else {
        echo "<p style='color: blue;'>✓ Income transactions already exist</p>";
    }
    
    echo "<h3 style='color: green;'>Database fix completed successfully!</h3>";
    echo "<p><a href='index.php'>Go to Dashboard</a> | <a href='transactions.php'>View Transactions</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error occurred:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>


