<?php
/**
 * Branch Access Control
 * Include this file in any branch-related page to ensure proper authentication
 */

// Check if user is logged in and has branch authentication
if (!isset($_SESSION['user_id']) || !isset($_SESSION['branch_authenticated'])) {
    header('Location: branch_login.php');
    exit;
}

// Check session timeout (8 hours)
if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 28800) {
    session_destroy();
    header('Location: branch_login.php?timeout=1');
    exit;
}

// Verify user has access to the requested branch (if branch parameter is provided)
if (isset($_GET['branch'])) {
    $requested_branch = $_GET['branch'];
    if ($_SESSION['role'] !== 'admin' && $_SESSION['branch_id'] != $requested_branch) {
        header('Location: branch_dashboard.php?branch=' . $_SESSION['branch_id']);
        exit;
    }
}

// Update last activity time
$_SESSION['last_activity'] = time();
?>
