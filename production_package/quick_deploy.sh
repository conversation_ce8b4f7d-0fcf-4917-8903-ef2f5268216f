#!/bin/bash

# Quick Deployment Script for Expense Management System
# Production Database: myclo4dz_expens

echo "🚀 Deploying Expense Management System to Production..."

# Database credentials
DB_NAME="myclo4dz_expens"
DB_USER="myclo4dz_myclo4dz_expens_user"
DB_PASS="uXy{HEXFcBfY"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if database exists
print_status "Checking database connection..."
mysql -u $DB_USER -p$DB_PASS -e "USE $DB_NAME;" 2>/dev/null

if [ $? -eq 0 ]; then
    print_success "Database connection successful"
else
    print_error "Database connection failed. Please check credentials."
    exit 1
fi

# Import database
print_status "Importing database..."
if [ -f "database_backup_production.sql" ]; then
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < database_backup_production.sql
    print_success "Production database imported"
elif [ -f "database_backup_max_compatible.sql" ]; then
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < database_backup_max_compatible.sql
    print_success "Compatible database imported"
else
    print_error "No database backup file found"
    exit 1
fi

# Set file permissions
print_status "Setting file permissions..."
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# Create necessary directories
mkdir -p logs cache backups
chmod 777 logs cache backups

print_success "File permissions set"

# Test application
print_status "Testing application..."
if curl -f http://localhost/ > /dev/null 2>&1; then
    print_success "Application is accessible"
else
    print_warning "Application test failed - check web server configuration"
fi

print_success "Deployment completed!"
echo ""
echo "🌐 Access your application at:"
echo "   https://online.mycloudforge.com/new_expns_app/"
echo ""
echo "🔐 Default login credentials:"
echo "   Username: admin"
echo "   Password: admin"
echo ""
echo "⚠️  Remember to change the default password after first login!"


