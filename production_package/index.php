<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'classes/Dashboard.php';

$dashboard = new Dashboard();
$summary = $dashboard->getDashboardSummary();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Management System - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-chart-line me-2"></i>Expense Management
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php">Transactions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="branches.php">Branches</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">Settings</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Alert Container -->
    <div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-0">Dashboard Overview</h1>
                <p class="text-muted">Monitor expenses, attendance, and enquiries across all branches</p>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total Expenses</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    ₹<?php echo number_format($summary['total_expenses'], 2); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total Students</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php 
                                    $totalStudents = array_sum(array_column($summary['student_stats'], 'total_students'));
                                    echo $totalStudents;
                                    ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Total Enquiries</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php 
                                    $totalEnquiries = array_sum(array_column($summary['enquiry_stats'], 'total_enquiries'));
                                    echo $totalEnquiries;
                                    ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Active Branches</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">4</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Statistics -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Branch-wise Expenses</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Branch</th>
                                        <th>Total Expenses</th>
                                        <th>Transactions</th>
                                        <th>Students</th>
                                        <th>Enquiries</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($summary['expenses_by_branch'] as $branch): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($branch['branch_name']); ?></strong>
                                        </td>
                                        <td>₹<?php echo number_format($branch['total_expenses'], 2); ?></td>
                                        <td><?php echo $branch['transaction_count']; ?></td>
                                        <td>
                                            <?php 
                                            $studentData = array_filter($summary['student_stats'], function($s) use ($branch) {
                                                return $s['branch_name'] === $branch['branch_name'];
                                            });
                                            echo !empty($studentData) ? array_values($studentData)[0]['total_students'] : 0;
                                            ?>
                                        </td>
                                        <td>
                                            <?php 
                                            $enquiryData = array_filter($summary['enquiry_stats'], function($e) use ($branch) {
                                                return $e['branch_name'] === $branch['branch_name'];
                                            });
                                            echo !empty($enquiryData) ? array_values($enquiryData)[0]['total_enquiries'] : 0;
                                            ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="add_transaction.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Transaction
                            </a>
                            <a href="branches.php" class="btn btn-outline-primary">
                                <i class="fas fa-building me-2"></i>Manage Branches
                            </a>
                            <a href="settings.php" class="btn btn-outline-secondary">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                            <a href="export.php" class="btn btn-outline-success">
                                <i class="fas fa-download me-2"></i>Export Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Recent Transactions</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Category</th>
                                        <th>Amount</th>
                                        <th>Branch</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($summary['recent_transactions'] as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('M d, Y', strtotime($transaction['date'])); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($transaction['category']); ?></span>
                                        </td>
                                        <td>₹<?php echo number_format($transaction['amount'], 2); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['branch_name']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light bg-white d-lg-none border-top">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branches.php" class="nav-link">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branches</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
