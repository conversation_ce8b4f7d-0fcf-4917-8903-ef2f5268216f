<?php
// Simple Database Configuration
// This file contains the production database credentials

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'myclo4dz_expens');
define('DB_USER', 'myclo4dz_myclo4dz_expens_user');
define('DB_PASS', 'uXy{HEXFcBfY');

// Test database connection
function testDatabaseConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        echo "Database connection failed: " . $e->getMessage();
        return null;
    }
}
?>
