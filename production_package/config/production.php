<?php
// Production Configuration for Expense Management System

// Base URL Configuration
define('BASE_URL', 'https://online.mycloudforge.com');
define('APP_URL', BASE_URL . '/new_expns_app');

// Database Configuration for Production
define('DB_HOST', 'localhost');
define('DB_NAME', 'myclo4dz_expens');
define('DB_USER', 'myclo4dz_myclo4dz_expens_user');
define('DB_PASS', 'uXy{HEXFcBfY');

// Security Configuration
define('ENCRYPTION_KEY', 'your-secure-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Backup Configuration
define('BACKUP_DIR', __DIR__ . '/../backups/');
define('BACKUP_RETENTION_DAYS', 30);
define('AUTO_BACKUP_ENABLED', true);
define('BACKUP_SCHEDULE', 'daily'); // daily, weekly, monthly

// Email Configuration (for notifications)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Expense Management System');

// File Upload Configuration
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);

// Logging Configuration
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', __DIR__ . '/../logs/app.log');

// Cache Configuration
define('CACHE_ENABLED', true);
define('CACHE_DIR', __DIR__ . '/../cache/');

// API Configuration
define('API_RATE_LIMIT', 100); // requests per hour
define('API_TIMEOUT', 30); // seconds

// Mobile Configuration
define('MOBILE_OPTIMIZED', true);
define('PWA_ENABLED', true);

// Performance Configuration
define('ENABLE_COMPRESSION', true);
define('ENABLE_CACHING', true);
define('MINIFY_CSS_JS', true);

// Monitoring Configuration
define('ENABLE_ANALYTICS', true);
define('ERROR_REPORTING', true);
define('PERFORMANCE_MONITORING', true);

// Backup URLs for different environments
$backup_urls = [
    'development' => 'http://localhost:3000',
    'staging' => 'https://staging.online.mycloudforge.com',
    'production' => 'https://online.mycloudforge.com'
];

// Get current environment
function getEnvironment() {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    if (strpos($host, 'localhost') !== false) {
        return 'development';
    } elseif (strpos($host, 'staging') !== false) {
        return 'staging';
    } else {
        return 'production';
    }
}

// Get base URL based on environment
function getBaseUrl() {
    global $backup_urls;
    $env = getEnvironment();
    return $backup_urls[$env] ?? $backup_urls['production'];
}

// Initialize production settings
if (getEnvironment() === 'production') {
    // Enable error reporting for production
    error_reporting(E_ALL);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_FILE);
    
    // Set timezone
    date_default_timezone_set('Asia/Kolkata');
    
    // Enable compression
    if (ENABLE_COMPRESSION && extension_loaded('zlib')) {
        ob_start('ob_gzhandler');
    }
}
?>
