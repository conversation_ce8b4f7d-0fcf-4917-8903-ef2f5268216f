<?php
require_once __DIR__ . '/../config/database.php';

class Transaction {
    private $conn;
    private $table = 'expenses';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Add new transaction
    public function addTransaction($branch_id, $amount, $description, $category, $date, $created_by) {
        $query = "INSERT INTO " . $this->table . " 
                  (branch_id, amount, description, category, date, created_by) 
                  VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$branch_id, $amount, $description, $category, $date, $created_by]);
    }

    // Get transactions by branch
    public function getTransactionsByBranch($branch_id, $limit = null) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  WHERE e.branch_id = ? 
                  ORDER BY e.date DESC, e.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get all transactions
    public function getAllTransactions($limit = null) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  ORDER BY e.date DESC, e.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get transaction by ID
    public function getTransactionById($id) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  WHERE e.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Update transaction
    public function updateTransaction($id, $amount, $description, $category, $date) {
        $query = "UPDATE " . $this->table . " 
                  SET amount = ?, description = ?, category = ?, date = ? 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$amount, $description, $category, $date, $id]);
    }

    // Delete transaction
    public function deleteTransaction($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    // Get transactions by date range
    public function getTransactionsByDateRange($start_date, $end_date, $branch_id = null) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  WHERE e.date BETWEEN ? AND ?";
        
        $params = [$start_date, $end_date];
        
        if ($branch_id) {
            $query .= " AND e.branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " ORDER BY e.date DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get total expenses by branch
    public function getTotalExpensesByBranch($branch_id) {
        $query = "SELECT SUM(amount) as total FROM " . $this->table . " WHERE branch_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    // Get expenses by category
    public function getExpensesByCategory($branch_id = null) {
        $query = "SELECT category, SUM(amount) as total, COUNT(*) as count 
                  FROM " . $this->table;
        
        $params = [];
        if ($branch_id) {
            $query .= " WHERE branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " GROUP BY category ORDER BY total DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
