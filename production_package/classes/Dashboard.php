<?php
require_once __DIR__ . '/../config/database.php';

class Dashboard {
    private $conn;
    private $table_expenses = 'expenses';
    private $table_branches = 'branches';
    private $table_students = 'students';
    private $table_attendance = 'attendance';
    private $table_enquiries = 'enquiries';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Get total expenses across all branches
    public function getTotalExpenses() {
        $query = "SELECT SUM(amount) as total FROM " . $this->table_expenses;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
    }

    // Get expenses by branch
    public function getExpensesByBranch() {
        $query = "SELECT b.name as branch_name, COALESCE(SUM(e.amount), 0) as total_expenses, 
                  COUNT(e.id) as transaction_count
                  FROM " . $this->table_branches . " b 
                  LEFT JOIN " . $this->table_expenses . " e ON b.id = e.branch_id 
                  GROUP BY b.id, b.name 
                  ORDER BY total_expenses DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get monthly expenses trend
    public function getMonthlyExpenses($months = 6) {
        $query = "SELECT DATE_FORMAT(date, '%Y-%m') as month, SUM(amount) as total 
                  FROM " . $this->table_expenses . " 
                  WHERE date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH) 
                  GROUP BY DATE_FORMAT(date, '%Y-%m') 
                  ORDER BY month";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$months]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get student statistics by branch
    public function getStudentStats() {
        $query = "SELECT b.name as branch_name, 
                  COUNT(s.id) as total_students,
                  COUNT(CASE WHEN a.status = 'present' AND a.date = CURDATE() THEN 1 END) as present_today,
                  COUNT(CASE WHEN a.status = 'absent' AND a.date = CURDATE() THEN 1 END) as absent_today
                  FROM " . $this->table_branches . " b 
                  LEFT JOIN " . $this->table_students . " s ON b.id = s.branch_id 
                  LEFT JOIN " . $this->table_attendance . " a ON s.id = a.student_id 
                  GROUP BY b.id, b.name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get enquiry statistics by branch
    public function getEnquiryStats() {
        $query = "SELECT b.name as branch_name,
                  COUNT(e.id) as total_enquiries,
                  COUNT(CASE WHEN e.status = 'new' THEN 1 END) as new_enquiries,
                  COUNT(CASE WHEN e.status = 'converted' THEN 1 END) as converted_enquiries
                  FROM " . $this->table_branches . " b 
                  LEFT JOIN " . $this->table_enquiries . " e ON b.id = e.branch_id 
                  GROUP BY b.id, b.name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get recent transactions
    public function getRecentTransactions($limit = 10) {
        $query = "SELECT e.amount, e.description, e.category, e.date, b.name as branch_name 
                  FROM " . $this->table_expenses . " e 
                  JOIN " . $this->table_branches . " b ON e.branch_id = b.id 
                  ORDER BY e.created_at DESC 
                  LIMIT " . (int)$limit;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get dashboard summary
    public function getDashboardSummary() {
        $totalExpenses = $this->getTotalExpenses();
        $expensesByBranch = $this->getExpensesByBranch();
        $studentStats = $this->getStudentStats();
        $enquiryStats = $this->getEnquiryStats();
        $recentTransactions = $this->getRecentTransactions();

        return [
            'total_expenses' => $totalExpenses,
            'expenses_by_branch' => $expensesByBranch,
            'student_stats' => $studentStats,
            'enquiry_stats' => $enquiryStats,
            'recent_transactions' => $recentTransactions
        ];
    }
}
?>
