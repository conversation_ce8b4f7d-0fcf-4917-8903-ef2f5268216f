<?php
require_once __DIR__ . '/../config/database.php';

class Student {
    private $conn;
    private $table_students = 'students';
    private $table_attendance = 'attendance';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Add new student
    public function addStudent($branch_id, $name, $roll_number, $class, $phone, $email, $address) {
        $query = "INSERT INTO " . $this->table_students . " 
                  (branch_id, name, roll_number, class, phone, email, address) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$branch_id, $name, $roll_number, $class, $phone, $email, $address]);
    }

    // Get students by branch
    public function getStudentsByBranch($branch_id) {
        $query = "SELECT * FROM " . $this->table_students . " 
                  WHERE branch_id = ? 
                  ORDER BY name ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$branch_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get student by ID
    public function getStudentById($id) {
        $query = "SELECT * FROM " . $this->table_students . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Update student
    public function updateStudent($id, $name, $roll_number, $class, $phone, $email, $address) {
        $query = "UPDATE " . $this->table_students . " 
                  SET name = ?, roll_number = ?, class = ?, phone = ?, email = ?, address = ? 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$name, $roll_number, $class, $phone, $email, $address, $id]);
    }

    // Delete student
    public function deleteStudent($id) {
        $query = "DELETE FROM " . $this->table_students . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    // Mark attendance
    public function markAttendance($student_id, $date, $status, $notes = '') {
        $query = "INSERT INTO " . $this->table_attendance . " 
                  (student_id, date, status, notes) 
                  VALUES (?, ?, ?, ?) 
                  ON DUPLICATE KEY UPDATE status = VALUES(status), notes = VALUES(notes)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$student_id, $date, $status, $notes]);
    }

    // Get attendance by date and branch
    public function getAttendanceByDate($branch_id, $date) {
        $query = "SELECT s.id, s.name, s.roll_number, s.class, 
                         a.status, a.notes, a.date
                  FROM " . $this->table_students . " s 
                  LEFT JOIN " . $this->table_attendance . " a ON s.id = a.student_id AND a.date = ?
                  WHERE s.branch_id = ? 
                  ORDER BY s.name ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$date, $branch_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get attendance statistics by branch
    public function getAttendanceStats($branch_id, $start_date = null, $end_date = null) {
        $query = "SELECT 
                    COUNT(DISTINCT s.id) as total_students,
                    COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
                    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count
                  FROM " . $this->table_students . " s 
                  LEFT JOIN " . $this->table_attendance . " a ON s.id = a.student_id";
        
        $params = [$branch_id];
        
        if ($start_date && $end_date) {
            $query .= " WHERE s.branch_id = ? AND a.date BETWEEN ? AND ?";
            $params[] = $start_date;
            $params[] = $end_date;
        } else {
            $query .= " WHERE s.branch_id = ?";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get attendance history for a student
    public function getStudentAttendanceHistory($student_id, $limit = 30) {
        $query = "SELECT date, status, notes 
                  FROM " . $this->table_attendance . " 
                  WHERE student_id = ? 
                  ORDER BY date DESC 
                  LIMIT " . (int)$limit;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$student_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
