<?php
require_once __DIR__ . '/../config/database.php';

class Enquiry {
    private $conn;
    private $table = 'enquiries';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Add new enquiry
    public function addEnquiry($branch_id, $name, $phone, $email, $subject, $message, $priority = 'medium') {
        $query = "INSERT INTO " . $this->table . " 
                  (branch_id, name, phone, email, subject, message, priority) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$branch_id, $name, $phone, $email, $subject, $message, $priority]);
    }

    // Get enquiries by branch
    public function getEnquiriesByBranch($branch_id, $status = null) {
        $query = "SELECT * FROM " . $this->table . " WHERE branch_id = ?";
        $params = [$branch_id];
        
        if ($status) {
            $query .= " AND status = ?";
            $params[] = $status;
        }
        
        $query .= " ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get all enquiries
    public function getAllEnquiries($status = null) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id";
        
        $params = [];
        if ($status) {
            $query .= " WHERE e.status = ?";
            $params[] = $status;
        }
        
        $query .= " ORDER BY e.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get enquiry by ID
    public function getEnquiryById($id) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  WHERE e.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Update enquiry status
    public function updateEnquiryStatus($id, $status) {
        $query = "UPDATE " . $this->table . " SET status = ? WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$status, $id]);
    }

    // Update enquiry
    public function updateEnquiry($id, $name, $phone, $email, $subject, $message, $status, $priority) {
        $query = "UPDATE " . $this->table . " 
                  SET name = ?, phone = ?, email = ?, subject = ?, message = ?, status = ?, priority = ? 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$name, $phone, $email, $subject, $message, $status, $priority, $id]);
    }

    // Delete enquiry
    public function deleteEnquiry($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    // Get enquiry statistics by branch
    public function getEnquiryStats($branch_id = null) {
        $query = "SELECT 
                    COUNT(*) as total_enquiries,
                    COUNT(CASE WHEN status = 'new' THEN 1 END) as new_enquiries,
                    COUNT(CASE WHEN status = 'contacted' THEN 1 END) as contacted_enquiries,
                    COUNT(CASE WHEN status = 'converted' THEN 1 END) as converted_enquiries,
                    COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_enquiries,
                    COUNT(CASE WHEN priority = 'high' THEN 1 END) as `high_priority`
                  FROM " . $this->table;
        
        $params = [];
        if ($branch_id) {
            $query .= " WHERE branch_id = ?";
            $params[] = $branch_id;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get recent enquiries
    public function getRecentEnquiries($limit = 10) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  ORDER BY e.created_at DESC 
                  LIMIT " . (int)$limit;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Search enquiries
    public function searchEnquiries($search_term, $branch_id = null) {
        $query = "SELECT e.*, b.name as branch_name 
                  FROM " . $this->table . " e 
                  JOIN branches b ON e.branch_id = b.id 
                  WHERE (e.name LIKE ? OR e.phone LIKE ? OR e.subject LIKE ? OR e.message LIKE ?)";
        
        $params = ["%$search_term%", "%$search_term%", "%$search_term%", "%$search_term%"];
        
        if ($branch_id) {
            $query .= " AND e.branch_id = ?";
            $params[] = $branch_id;
        }
        
        $query .= " ORDER BY e.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
