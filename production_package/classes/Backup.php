<?php
require_once __DIR__ . '/../config/database.php';

class Backup {
    private $conn;
    private $backup_dir;
    private $base_url = 'https://online.mycloudforge.com';
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->backup_dir = __DIR__ . '/../backups/';
        
        // Create backup directory if it doesn't exist
        if (!file_exists($this->backup_dir)) {
            mkdir($this->backup_dir, 0755, true);
        }
    }
    
    // Create full database backup
    public function createDatabaseBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = $this->backup_dir . "database_backup_{$timestamp}.sql";
        
        // Get database credentials
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $database = 'expense_management';
        
        // Create mysqldump command
        $command = "mysqldump -h {$host} -u {$username}";
        if (!empty($password)) {
            $command .= " -p{$password}";
        }
        $command .= " {$database} > {$backup_file}";
        
        // Execute backup command
        $output = [];
        $return_code = 0;
        exec($command, $output, $return_code);
        
        if ($return_code === 0 && file_exists($backup_file)) {
            return [
                'success' => true,
                'file' => $backup_file,
                'size' => filesize($backup_file),
                'timestamp' => $timestamp
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to create database backup'
            ];
        }
    }
    
    // Create application files backup
    public function createApplicationBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = $this->backup_dir . "application_backup_{$timestamp}.zip";
        
        // Files to include in backup
        $files_to_backup = [
            'classes/',
            'config/',
            'assets/',
            'api/',
            'database/',
            'index.php',
            'transactions.php',
            'branch_dashboard.php',
            'branches.php',
            'settings.php',
            'login.php',
            'logout.php',
            '.htaccess'
        ];
        
        $zip = new ZipArchive();
        if ($zip->open($backup_file, ZipArchive::CREATE) === TRUE) {
            foreach ($files_to_backup as $file) {
                if (file_exists($file)) {
                    if (is_dir($file)) {
                        $this->addDirectoryToZip($zip, $file, '');
                    } else {
                        $zip->addFile($file, $file);
                    }
                }
            }
            $zip->close();
            
            if (file_exists($backup_file)) {
                return [
                    'success' => true,
                    'file' => $backup_file,
                    'size' => filesize($backup_file),
                    'timestamp' => $timestamp
                ];
            }
        }
        
        return [
            'success' => false,
            'error' => 'Failed to create application backup'
        ];
    }
    
    // Add directory to zip recursively
    private function addDirectoryToZip($zip, $dir, $zip_dir) {
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $file_path = $dir . '/' . $file;
                $zip_path = $zip_dir . $file;
                
                if (is_dir($file_path)) {
                    $zip->addEmptyDir($zip_path);
                    $this->addDirectoryToZip($zip, $file_path, $zip_path . '/');
                } else {
                    $zip->addFile($file_path, $zip_path);
                }
            }
        }
    }
    
    // Create complete system backup
    public function createFullBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $backup_dir = $this->backup_dir . "full_backup_{$timestamp}/";
        
        if (!file_exists($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        // Create database backup
        $db_backup = $this->createDatabaseBackup();
        if ($db_backup['success']) {
            copy($db_backup['file'], $backup_dir . basename($db_backup['file']));
        }
        
        // Create application backup
        $app_backup = $this->createApplicationBackup();
        if ($app_backup['success']) {
            copy($app_backup['file'], $backup_dir . basename($app_backup['file']));
        }
        
        // Create backup info file
        $info = [
            'timestamp' => $timestamp,
            'database_backup' => $db_backup,
            'application_backup' => $app_backup,
            'base_url' => $this->base_url,
            'created_by' => 'system'
        ];
        
        file_put_contents($backup_dir . 'backup_info.json', json_encode($info, JSON_PRETTY_PRINT));
        
        return [
            'success' => true,
            'directory' => $backup_dir,
            'timestamp' => $timestamp,
            'database_backup' => $db_backup,
            'application_backup' => $app_backup
        ];
    }
    
    // List all backups
    public function listBackups() {
        $backups = [];
        
        if (is_dir($this->backup_dir)) {
            $files = scandir($this->backup_dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $file_path = $this->backup_dir . $file;
                    $backups[] = [
                        'name' => $file,
                        'size' => filesize($file_path),
                        'created' => date('Y-m-d H:i:s', filemtime($file_path)),
                        'type' => $this->getBackupType($file)
                    ];
                }
            }
        }
        
        // Sort by creation time (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['created']) - strtotime($a['created']);
        });
        
        return $backups;
    }
    
    // Get backup type
    private function getBackupType($filename) {
        if (strpos($filename, 'database_backup_') === 0) {
            return 'database';
        } elseif (strpos($filename, 'application_backup_') === 0) {
            return 'application';
        } elseif (strpos($filename, 'full_backup_') === 0) {
            return 'full';
        }
        return 'unknown';
    }
    
    // Clean old backups (keep only last 30 days)
    public function cleanOldBackups($days = 30) {
        $cutoff_time = time() - ($days * 24 * 60 * 60);
        $cleaned = 0;
        
        if (is_dir($this->backup_dir)) {
            $files = scandir($this->backup_dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $file_path = $this->backup_dir . $file;
                    if (filemtime($file_path) < $cutoff_time) {
                        if (is_dir($file_path)) {
                            $this->deleteDirectory($file_path);
                        } else {
                            unlink($file_path);
                        }
                        $cleaned++;
                    }
                }
            }
        }
        
        return $cleaned;
    }
    
    // Delete directory recursively
    private function deleteDirectory($dir) {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), array('.', '..'));
            foreach ($files as $file) {
                $file_path = $dir . '/' . $file;
                if (is_dir($file_path)) {
                    $this->deleteDirectory($file_path);
                } else {
                    unlink($file_path);
                }
            }
            rmdir($dir);
        }
    }
    
    // Get backup statistics
    public function getBackupStats() {
        $backups = $this->listBackups();
        $total_size = 0;
        $type_counts = ['database' => 0, 'application' => 0, 'full' => 0];
        
        foreach ($backups as $backup) {
            $total_size += $backup['size'];
            $type_counts[$backup['type']]++;
        }
        
        return [
            'total_backups' => count($backups),
            'total_size' => $total_size,
            'total_size_mb' => round($total_size / (1024 * 1024), 2),
            'type_counts' => $type_counts,
            'oldest_backup' => count($backups) > 0 ? $backups[count($backups) - 1]['created'] : null,
            'newest_backup' => count($backups) > 0 ? $backups[0]['created'] : null
        ];
    }
}
?>
