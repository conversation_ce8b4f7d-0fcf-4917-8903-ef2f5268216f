<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'classes/Dashboard.php';
require_once 'classes/Transaction.php';
require_once 'classes/Student.php';
require_once 'classes/Enquiry.php';

$dashboard = new Dashboard();
$transaction = new Transaction();
$student = new Student();
$enquiry = new Enquiry();

// Handle CSV export
if (isset($_POST['export_type'])) {
    $export_type = $_POST['export_type'];
    $branch_id = $_POST['branch_id'] ?? null;
    
    switch($export_type) {
        case 'all_expenses':
            $data = $transaction->getAllTransactions();
            $filename = 'all_expenses_' . date('Y-m-d') . '.csv';
            break;
        case 'branch_expenses':
            $data = $transaction->getTransactionsByBranch($branch_id);
            $branch_name = ['1' => 'Uppala', '2' => 'Seethangoli', '3' => 'Kasaragod', '4' => 'Kochi'][$branch_id];
            $filename = $branch_name . '_expenses_' . date('Y-m-d') . '.csv';
            break;
        case 'all_students':
            $data = [];
            for($i = 1; $i <= 4; $i++) {
                $students = $student->getStudentsByBranch($i);
                foreach($students as $s) {
                    $s['branch_name'] = ['1' => 'Uppala', '2' => 'Seethangoli', '3' => 'Kasaragod', '4' => 'Kochi'][$i];
                    $data[] = $s;
                }
            }
            $filename = 'all_students_' . date('Y-m-d') . '.csv';
            break;
        case 'all_enquiries':
            $data = $enquiry->getAllEnquiries();
            $filename = 'all_enquiries_' . date('Y-m-d') . '.csv';
            break;
        default:
            $data = [];
            $filename = 'export_' . date('Y-m-d') . '.csv';
    }
    
    // Export CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    if (!empty($data)) {
        $output = fopen('php://output', 'w');
        fputcsv($output, array_keys($data[0]));
        foreach($data as $row) {
            fputcsv($output, $row);
        }
        fclose($output);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-chart-line me-2"></i>Expense Management
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php">Transactions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="branches.php">Branches</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="settings.php">Settings</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-0">Settings & Data Management</h1>
                <p class="text-muted">Configure system settings and manage data backup</p>
            </div>
        </div>

        <div class="row">
            <!-- Data Export Section -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Data Export & Backup</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="row g-3">
                            <div class="col-md-6">
                                <label for="export_type" class="form-label">Export Type</label>
                                <select class="form-select" id="export_type" name="export_type" required>
                                    <option value="">Select Export Type</option>
                                    <option value="all_expenses">All Expenses</option>
                                    <option value="branch_expenses">Branch Expenses</option>
                                    <option value="all_students">All Students</option>
                                    <option value="all_enquiries">All Enquiries</option>
                                </select>
                            </div>
                            <div class="col-md-6" id="branch_selection" style="display: none;">
                                <label for="branch_id" class="form-label">Select Branch</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="">Select Branch</option>
                                    <option value="1">Uppala</option>
                                    <option value="2">Seethangoli</option>
                                    <option value="3">Kasaragod</option>
                                    <option value="4">Kochi</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>Export CSV
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- System Statistics -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">System Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Database Summary</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-database text-primary me-2"></i>Total Expenses: ₹<?php echo number_format($dashboard->getTotalExpenses(), 2); ?></li>
                                    <li><i class="fas fa-building text-info me-2"></i>Active Branches: 4</li>
                                    <li><i class="fas fa-users text-success me-2"></i>Total Students: <?php 
                                        $totalStudents = 0;
                                        for($i = 1; $i <= 4; $i++) {
                                            $totalStudents += count($student->getStudentsByBranch($i));
                                        }
                                        echo $totalStudents;
                                    ?></li>
                                    <li><i class="fas fa-question-circle text-warning me-2"></i>Total Enquiries: <?php 
                                        $enquiryStats = $enquiry->getEnquiryStats();
                                        echo $enquiryStats['total_enquiries'];
                                    ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Branch-wise Summary</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Branch</th>
                                                <th>Expenses</th>
                                                <th>Students</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $branches = ['1' => 'Uppala', '2' => 'Seethangoli', '3' => 'Kasaragod', '4' => 'Kochi'];
                                            foreach($branches as $id => $name): 
                                                $expenses = $transaction->getTotalExpensesByBranch($id);
                                                $students = count($student->getStudentsByBranch($id));
                                            ?>
                                            <tr>
                                                <td><?php echo $name; ?></td>
                                                <td>₹<?php echo number_format($expenses, 2); ?></td>
                                                <td><?php echo $students; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Panel -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">System Settings</h6>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <label for="app_name" class="form-label">Application Name</label>
                                <input type="text" class="form-control" id="app_name" value="Expense Management System">
                            </div>
                            <div class="mb-3">
                                <label for="backup_frequency" class="form-label">Backup Frequency</label>
                                <select class="form-select" id="backup_frequency">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="max_file_size" class="form-label">Max File Size</label>
                                <select class="form-select" id="max_file_size">
                                    <option value="10MB">10MB</option>
                                    <option value="50MB">50MB</option>
                                    <option value="100MB">100MB</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_backup" checked>
                                    <label class="form-check-label" for="auto_backup">
                                        Enable Auto Backup
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                    <label class="form-check-label" for="email_notifications">
                                        Email Notifications
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="createBackup()">
                                <i class="fas fa-database me-2"></i>Create Full Backup
                            </button>
                            <button class="btn btn-outline-success" onclick="exportAllData()">
                                <i class="fas fa-download me-2"></i>Export All Data
                            </button>
                            <a href="backup_manager.php" class="btn btn-outline-primary">
                                <i class="fas fa-database me-2"></i>Backup Manager
                            </a>
                            <button class="btn btn-outline-warning" onclick="clearOldData()">
                                <i class="fas fa-trash me-2"></i>Clear Old Data
                            </button>
                            <button class="btn btn-outline-info" onclick="viewLogs()">
                                <i class="fas fa-file-alt me-2"></i>View System Logs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Permissions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Branch Permissions</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Branch</th>
                                        <th>Student Management</th>
                                        <th>Attendance System</th>
                                        <th>Enquiry System</th>
                                        <th>Expense Management</th>
                                        <th>Reports</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($branches as $id => $name): ?>
                                    <tr>
                                        <td><strong><?php echo $name; ?></strong></td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="student_<?php echo $id; ?>" checked>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="attendance_<?php echo $id; ?>" checked>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enquiry_<?php echo $id; ?>" checked>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="expense_<?php echo $id; ?>" checked>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="reports_<?php echo $id; ?>" checked>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="savePermissions()">
                                <i class="fas fa-save me-2"></i>Save Permissions
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light bg-white d-lg-none border-top">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branches.php" class="nav-link">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branches</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link active">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // Show/hide branch selection based on export type
        document.getElementById('export_type').addEventListener('change', function() {
            const branchSelection = document.getElementById('branch_selection');
            if (this.value === 'branch_expenses') {
                branchSelection.style.display = 'block';
                document.getElementById('branch_id').required = true;
            } else {
                branchSelection.style.display = 'none';
                document.getElementById('branch_id').required = false;
            }
        });

        // Quick action functions
        function createBackup() {
            if (confirm('Create a full system backup? This may take a few minutes.')) {
                showLoading(document.activeElement);
                // Simulate backup process
                setTimeout(() => {
                    hideLoading(document.activeElement);
                    showSuccess('Backup created successfully!');
                }, 2000);
            }
        }

        function exportAllData() {
            if (confirm('Export all system data? This will create multiple CSV files.')) {
                showLoading(document.activeElement);
                // Simulate export process
                setTimeout(() => {
                    hideLoading(document.activeElement);
                    showSuccess('All data exported successfully!');
                }, 3000);
            }
        }

        function clearOldData() {
            if (confirm('Clear old data (older than 1 year)? This action cannot be undone.')) {
                if (confirm('Are you absolutely sure? This will permanently delete old records.')) {
                    showLoading(document.activeElement);
                    // Simulate cleanup process
                    setTimeout(() => {
                        hideLoading(document.activeElement);
                        showSuccess('Old data cleared successfully!');
                    }, 2000);
                }
            }
        }

        function viewLogs() {
            alert('System logs feature coming soon!');
        }

        function savePermissions() {
            showLoading(document.activeElement);
            // Simulate saving permissions
            setTimeout(() => {
                hideLoading(document.activeElement);
                showSuccess('Permissions saved successfully!');
            }, 1000);
        }
    </script>
</body>
</html>
