<?php
// Database Connection Test Script
// This script tests the database connection with your production credentials

require_once 'config/database_config.php';

echo "<h2>Database Connection Test</h2>";
echo "<p><strong>Testing connection to:</strong></p>";
echo "<ul>";
echo "<li>Host: " . DB_HOST . "</li>";
echo "<li>Database: " . DB_NAME . "</li>";
echo "<li>User: " . DB_USER . "</li>";
echo "<li>Password: " . str_repeat('*', strlen(DB_PASS)) . "</li>";
echo "</ul>";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "<p style='color: green;'><strong>✅ Database connection successful!</strong></p>";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'");
    $result = $stmt->fetch();
    echo "<p>Tables in database: " . $result['table_count'] . "</p>";
    
    // List tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>Available tables:</strong></p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . $table . "</li>";
    }
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'><strong>❌ Database connection failed!</strong></p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Possible solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check if database 'myclo4dz_expens' exists</li>";
    echo "<li>Verify username and password</li>";
    echo "<li>Check if MySQL server is running</li>";
    echo "<li>Import the database backup first</li>";
    echo "</ul>";
}
?>
