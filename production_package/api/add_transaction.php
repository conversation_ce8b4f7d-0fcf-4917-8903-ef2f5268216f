<?php
header('Content-Type: application/json');
session_start();

require_once '../classes/Transaction.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $transaction = new Transaction();
    
    $branch_id = $_POST['branch_id'] ?? null;
    $amount = $_POST['amount'] ?? null;
    $description = $_POST['description'] ?? null;
    $category = $_POST['category'] ?? null;
    $date = $_POST['date'] ?? null;
    $created_by = 1; // Assuming admin user ID
    
    // Validate required fields
    if (!$branch_id || !$amount || !$description || !$category || !$date) {
        echo json_encode(['success' => false, 'message' => 'All fields are required']);
        exit;
    }
    
    // Validate amount
    if (!is_numeric($amount) || $amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Amount must be a positive number']);
        exit;
    }
    
    try {
        $result = $transaction->addTransaction($branch_id, $amount, $description, $category, $date, $created_by);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Transaction added successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add transaction']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
