<?php
header('Content-Type: application/json');
session_start();

require_once '../classes/Transaction.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $transaction = new Transaction();
    
    $transaction_id = $_POST['transaction_id'] ?? null;
    $amount = $_POST['amount'] ?? null;
    $description = $_POST['description'] ?? null;
    $category = $_POST['category'] ?? null;
    $date = $_POST['date'] ?? null;
    
    // Validate required fields
    if (!$transaction_id || !$amount || !$description || !$category || !$date) {
        echo json_encode(['success' => false, 'message' => 'All fields are required']);
        exit;
    }
    
    // Validate amount
    if (!is_numeric($amount) || $amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Amount must be a positive number']);
        exit;
    }
    
    try {
        $result = $transaction->updateTransaction($transaction_id, $amount, $description, $category, $date);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Transaction updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update transaction']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
