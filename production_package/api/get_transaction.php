<?php
header('Content-Type: application/json');
session_start();

require_once '../classes/Transaction.php';

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $transaction = new Transaction();
    
    $transaction_id = $_GET['id'] ?? null;
    
    if (!$transaction_id) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
        exit;
    }
    
    try {
        $result = $transaction->getTransactionById($transaction_id);
        
        if ($result) {
            echo json_encode(['success' => true, 'data' => $result]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Transaction not found']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
