# Production Deployment Instructions

## 🚀 **Expense Management System - Production Package**

**Database**: `myclo4dz_expens`  
**User**: `myclo4dz_myclo4dz_expens_user`  
**Password**: `uXy{HEXFcBfY`  
**Base URL**: `https://online.mycloudforge.com`

## 📋 **Quick Deployment Steps**

### 1. **Upload Files**
- Upload all files to your web server
- Ensure files are in the correct directory structure
- Set proper file permissions (755 for directories, 644 for files)

### 2. **Database Setup**
```sql
-- Create database (if not exists)
CREATE DATABASE myclo4dz_expens CHARACTER SET utf8 COLLATE utf8_general_ci;

-- Import the database
mysql -u myclo4dz_myclo4dz_expens_user -p myclo4dz_expens < database_backup_production.sql
```

### 3. **Database Import Options**

#### **Option A: Use Production Backup**
- File: `database_backup_production.sql`
- Pre-configured for your database name
- Compatible with most MySQL versions

#### **Option B: Use Compatible Backup**
- File: `database_backup_max_compatible.sql`
- Maximum compatibility (MySQL 5.5+)
- Use if you get collation errors

### 4. **Configuration**
The system is pre-configured with your production credentials:
- Database: `myclo4dz_expens`
- User: `myclo4dz_myclo4dz_expens_user`
- Password: `uXy{HEXFcBfY`
- Base URL: `https://online.mycloudforge.com`

### 5. **File Permissions**
```bash
# Set proper permissions
chmod 755 api/ assets/ classes/ config/ database/
chmod 644 *.php .htaccess
chmod 777 logs/ cache/ backups/ (if these directories exist)
```

## 🔧 **Database Import Methods**

### **Method 1: phpMyAdmin**
1. Login to phpMyAdmin
2. Select database `myclo4dz_expens`
3. Click "Import" tab
4. Choose `database_backup_production.sql`
5. Click "Go"

### **Method 2: Command Line**
```bash
mysql -u myclo4dz_myclo4dz_expens_user -p myclo4dz_expens < database_backup_production.sql
```

### **Method 3: MySQL Workbench**
1. Connect to your MySQL server
2. Open SQL script: `database_backup_production.sql`
3. Execute the script

## 📊 **Database Contents**

The backup includes:
- ✅ **7 Tables**: branches, expenses, students, enquiries, users, settings, attendance
- ✅ **25 Records**: Complete sample data
- ✅ **₹17,500+**: Sample expenses across all branches
- ✅ **4 Branches**: Uppala, Seethangoli, Kasaragod, Kochi
- ✅ **5 Students**: Sample student records
- ✅ **4 Enquiries**: Customer enquiry examples

## 🎯 **Post-Deployment Checklist**

- [ ] Files uploaded to server
- [ ] Database imported successfully
- [ ] File permissions set correctly
- [ ] Application accessible via web browser
- [ ] Login working (admin/admin)
- [ ] All features functional
- [ ] Mobile responsive design working

## 🔐 **Default Login Credentials**

- **Username**: `admin`
- **Password**: `admin`

**Important**: Change these credentials after first login!

## 📱 **Access URLs**

- **Main Application**: `https://online.mycloudforge.com/new_expns_app/`
- **Login Page**: `https://online.mycloudforge.com/new_expns_app/login.php`
- **Backup Manager**: `https://online.mycloudforge.com/new_expns_app/backup_manager.php`

## 🛠️ **Troubleshooting**

### **Database Connection Issues**
- Verify database credentials in `config/database.php`
- Check if database exists: `myclo4dz_expens`
- Test connection: `mysql -u myclo4dz_myclo4dz_expens_user -p`

### **Permission Issues**
- Set correct file permissions (755/644)
- Ensure web server can read files
- Check directory ownership

### **Collation Errors**
- Use `database_backup_max_compatible.sql` instead
- This version works with older MySQL versions

## 🚀 **Your System is Ready!**

Once deployed, your Expense Management System will be fully functional with:
- ✅ Main Dashboard with real-time statistics
- ✅ Transaction Management (Add/Edit/Delete)
- ✅ Branch Management (4 branches)
- ✅ Student Attendance System
- ✅ Smart Enquiries System
- ✅ Data Export (CSV)
- ✅ Backup Management
- ✅ Mobile Responsive Design

**Deployment Complete!** 🎉


