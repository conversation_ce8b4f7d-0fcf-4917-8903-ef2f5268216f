<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'classes/Dashboard.php';
require_once 'classes/Transaction.php';
require_once 'classes/Student.php';
require_once 'classes/Enquiry.php';

$dashboard = new Dashboard();
$transaction = new Transaction();
$student = new Student();
$enquiry = new Enquiry();

// Get branch data
$branches = [
    ['id' => 1, 'name' => 'Uppala', 'location' => 'Uppala'],
    ['id' => 2, 'name' => 'Seethangoli', 'location' => 'Seethangoli'],
    ['id' => 3, 'name' => 'Kasaragod', 'location' => 'Kasaragod'],
    ['id' => 4, 'name' => 'Kochi', 'location' => 'Kochi']
];

// Get statistics for each branch
$branch_stats = [];
foreach($branches as $branch) {
    $expenses = $transaction->getTotalExpensesByBranch($branch['id']);
    $students = $student->getStudentsByBranch($branch['id']);
    $enquiries = $enquiry->getEnquiriesByBranch($branch['id']);
    $attendance_stats = $student->getAttendanceStats($branch['id']);
    
    $branch_stats[] = [
        'id' => $branch['id'],
        'name' => $branch['name'],
        'location' => $branch['location'],
        'expenses' => $expenses,
        'student_count' => count($students),
        'enquiry_count' => count($enquiries),
        'attendance_rate' => $attendance_stats['total_students'] > 0 ? 
            round(($attendance_stats['present_count'] / $attendance_stats['total_students']) * 100, 1) : 0
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Management - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-chart-line me-2"></i>Expense Management
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php">Transactions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="branches.php">Branches</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">Settings</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-0">Branch Management</h1>
                <p class="text-muted">Overview of all branches and their performance</p>
            </div>
        </div>

        <!-- Branch Cards -->
        <div class="row mb-4">
            <?php foreach($branch_stats as $branch): ?>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card branch-card shadow h-100" onclick="viewBranch(<?php echo $branch['id']; ?>)">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    <?php echo $branch['name']; ?> Branch</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo $branch['location']; ?>
                                </div>
                                <div class="text-xs text-muted mt-1">
                                    <i class="fas fa-rupee-sign me-1"></i>₹<?php echo number_format($branch['expenses'], 2); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <div class="text-xs text-muted">Students</div>
                                <div class="h6 mb-0"><?php echo $branch['student_count']; ?></div>
                            </div>
                            <div class="col-6">
                                <div class="text-xs text-muted">Enquiries</div>
                                <div class="h6 mb-0"><?php echo $branch['enquiry_count']; ?></div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="text-xs text-muted">Attendance Rate</div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: <?php echo $branch['attendance_rate']; ?>%">
                                </div>
                            </div>
                            <small class="text-muted"><?php echo $branch['attendance_rate']; ?>%</small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <a href="branch_dashboard.php?branch=<?php echo $branch['id']; ?>" 
                           class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-arrow-right me-2"></i>View Details
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Branch Comparison Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Branch Performance Comparison</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Branch</th>
                                        <th>Total Expenses</th>
                                        <th>Students</th>
                                        <th>Enquiries</th>
                                        <th>Attendance Rate</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($branch_stats as $branch): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $branch['name']; ?></strong>
                                            <br><small class="text-muted"><?php echo $branch['location']; ?></small>
                                        </td>
                                        <td class="fw-bold">₹<?php echo number_format($branch['expenses'], 2); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $branch['student_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo $branch['enquiry_count']; ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-<?php 
                                                        echo $branch['attendance_rate'] >= 80 ? 'success' : 
                                                            ($branch['attendance_rate'] >= 60 ? 'warning' : 'danger'); 
                                                    ?>" style="width: <?php echo $branch['attendance_rate']; ?>%">
                                                    </div>
                                                </div>
                                                <span><?php echo $branch['attendance_rate']; ?>%</span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php 
                                            $performance = 'Good';
                                            $badge_class = 'success';
                                            if ($branch['attendance_rate'] < 60) {
                                                $performance = 'Poor';
                                                $badge_class = 'danger';
                                            } elseif ($branch['attendance_rate'] < 80) {
                                                $performance = 'Average';
                                                $badge_class = 'warning';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $badge_class; ?>"><?php echo $performance; ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="branch_dashboard.php?branch=<?php echo $branch['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewReports(<?php echo $branch['id']; ?>)">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="manageBranch(<?php echo $branch['id']; ?>)">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total Branches</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">4</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total Students</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo array_sum(array_column($branch_stats, 'student_count')); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Total Enquiries</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo array_sum(array_column($branch_stats, 'enquiry_count')); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Avg Attendance</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php 
                                    $avg_attendance = array_sum(array_column($branch_stats, 'attendance_rate')) / count($branch_stats);
                                    echo round($avg_attendance, 1); 
                                    ?>%
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light bg-white d-lg-none border-top">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branches.php" class="nav-link active">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branches</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function viewBranch(branchId) {
            window.location.href = `branch_dashboard.php?branch=${branchId}`;
        }
        
        function viewReports(branchId) {
            alert('Reports feature coming soon for branch ' + branchId);
        }
        
        function manageBranch(branchId) {
            alert('Branch management feature coming soon for branch ' + branchId);
        }
    </script>
</body>
</html>
