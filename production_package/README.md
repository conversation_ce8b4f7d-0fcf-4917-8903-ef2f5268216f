# Expense Management System

A comprehensive, professional, and user-friendly Expense Management App with a Main Dashboard and four branches (Uppala, Seethangoli, Kasaragod, and Kochi).

## Features

### Main Dashboard
- **Total Expenses Overview**: Real-time display of total expenses across all branches
- **Branch-wise Statistics**: Detailed breakdown of expenses, students, and enquiries for each branch
- **Transaction Management**: Add, edit, and delete transactions with instant dashboard updates
- **Branch Permission Controls**: Configure which features each branch can access
- **Settings & Data Backup**: Export data to CSV files (overall and branch-wise)

### Branch Dashboards
Each branch (Uppala, Seethangoli, Kasaragod, Kochi) includes:
- **Student Attendance System**: Manage student records and track daily attendance
- **Smart Enquiries System**: Handle customer enquiries with status tracking and priority management
- **Branch-specific Expense Management**: Track and manage expenses for each branch
- **Real-time Statistics**: Attendance rates, enquiry conversion, and expense summaries

### Mobile-Responsive Design
- **Bottom Navigation Bar**: Smooth, app-like user experience on mobile devices
- **Responsive Layout**: Optimized for both desktop and mobile viewing
- **Touch-friendly Interface**: Easy navigation and interaction on all devices

## Technology Stack
- **Backend**: PHP 7.4+
- **Frontend**: Bootstrap 5.3, HTML5, CSS3, JavaScript
- **Database**: MySQL 5.7+
- **Icons**: Font Awesome 6.0

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Composer (optional, for dependency management)

### Setup Instructions

1. **Clone/Download the Project**
   ```bash
   git clone <repository-url>
   cd new_expns_app
   ```

2. **Database Setup**
   - Create a MySQL database named `expense_management`
   - Import the database schema:
   ```bash
   mysql -u your_username -p expense_management < database/schema.sql
   ```

3. **Configure Database Connection**
   - Edit `config/database.php` with your database credentials:
   ```php
   private $host = 'localhost';
   private $db_name = 'expense_management';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

4. **Set Permissions**
   ```bash
   chmod 755 assets/
   chmod 644 assets/css/style.css
   chmod 644 assets/js/main.js
   ```

5. **Access the Application**
   - Open your web browser
   - Navigate to `http://your-domain/new_expns_app/`
   - Default admin credentials: username: `admin`, password: `password`

## File Structure

```
new_expns_app/
├── api/                          # API endpoints
│   ├── add_transaction.php
│   ├── update_transaction.php
│   ├── delete_transaction.php
│   ├── get_transaction.php
│   └── dashboard_data.php
├── assets/                       # Static assets
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
├── classes/                      # PHP classes
│   ├── Database.php
│   ├── Dashboard.php
│   ├── Transaction.php
│   ├── Student.php
│   └── Enquiry.php
├── config/                       # Configuration files
│   └── database.php
├── database/                     # Database schema
│   └── schema.sql
├── index.php                     # Main dashboard
├── transactions.php              # Transaction management
├── branch_dashboard.php          # Branch-specific dashboard
├── branches.php                  # Branch overview
├── settings.php                  # Settings and data export
└── README.md                     # This file
```

## Usage Guide

### Main Dashboard
1. **View Overview**: Access total expenses, branch statistics, and recent transactions
2. **Add Transactions**: Click "Add Transaction" to record new expenses
3. **Manage Branches**: Navigate to branch-specific dashboards
4. **Export Data**: Use Settings page to export CSV files

### Branch Management
1. **Select Branch**: Choose from Uppala, Seethangoli, Kasaragod, or Kochi
2. **Student Management**: Add students, mark attendance, view records
3. **Enquiry System**: Handle customer enquiries with status tracking
4. **Expense Tracking**: Manage branch-specific expenses

### Mobile Usage
- **Bottom Navigation**: Use the bottom navigation bar for easy mobile access
- **Responsive Design**: All features work seamlessly on mobile devices
- **Touch Interface**: Optimized for touch interactions

## Features in Detail

### Student Attendance System
- Add/Edit/Delete student records
- Mark daily attendance (Present/Absent/Late)
- View attendance history and statistics
- Branch-wise attendance tracking

### Smart Enquiries System
- Record customer enquiries with contact details
- Track enquiry status (New/Contacted/Converted/Closed)
- Set priority levels (High/Medium/Low)
- Search and filter enquiries

### Transaction Management
- Add expenses with categories and descriptions
- Real-time dashboard updates
- Branch-wise expense tracking
- Export capabilities

### Data Export & Backup
- Export all expenses to CSV
- Branch-specific data export
- Student data export
- Enquiry data export
- System settings management

## Security Features
- SQL injection prevention with prepared statements
- Input validation and sanitization
- Session management
- Permission-based access control

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Troubleshooting

### Common Issues
1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Permission Errors**
   - Set proper file permissions (755 for directories, 644 for files)
   - Check web server user permissions

3. **Mobile Navigation Issues**
   - Clear browser cache
   - Ensure JavaScript is enabled
   - Check for console errors

### Support
For technical support or feature requests, please contact the development team.

## License
This project is proprietary software. All rights reserved.

## Version
Current Version: 1.0.0
Last Updated: 2024
