<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'classes/Transaction.php';

$transaction = new Transaction();

// Process filter parameters
$filters = [];
if (isset($_GET['branch']) && !empty($_GET['branch'])) {
    $filters['branch'] = $_GET['branch'];
}
if (isset($_GET['category']) && !empty($_GET['category'])) {
    $filters['category'] = $_GET['category'];
}
if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
    $filters['start_date'] = $_GET['start_date'];
}
if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
    $filters['end_date'] = $_GET['end_date'];
}

$transactions = $transaction->getAllTransactions($filters);

// Check if coming from dashboard
$fromDashboard = isset($_GET['source']) && $_GET['source'] === 'dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Management - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand premium-brand" href="index.php">
                <div class="brand-text">
                    <span class="brand-main">KFT Manager</span>
                </div>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php" title="Dashboard">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="transactions.php" title="Transactions">
                            <i class="fas fa-receipt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="branches.php" title="Branches">
                            <i class="fas fa-building"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php" title="Settings">
                            <i class="fas fa-cog"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Alert Container -->
    <div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>

    <!-- Main Content -->
    <div class="main-container" style="margin-top: 85px;">
        <!-- Page Header -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Transaction Management</h1>
                        <p class="text-muted">Manage all expenses across branches</p>
                    </div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                        <i class="fas fa-plus me-2"></i>Add Transaction
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <button class="btn btn-link text-decoration-none p-0 w-100 text-start" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-filter me-2"></i>
                                    <strong>Filter Transactions</strong>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </button>
                    </div>
                    <div class="collapse" id="filterCollapse">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="branch_filter" class="form-label">Branch</label>
                                    <select class="form-select" id="branch_filter" name="branch">
                                        <option value="">All Branches</option>
                                        <option value="main" <?php echo (isset($_GET['branch']) && $_GET['branch'] === 'main') ? 'selected' : ''; ?>>Main</option>
                                        <option value="1" <?php echo (isset($_GET['branch']) && $_GET['branch'] === '1') ? 'selected' : ''; ?>>Uppala</option>
                                        <option value="2" <?php echo (isset($_GET['branch']) && $_GET['branch'] === '2') ? 'selected' : ''; ?>>Seethangoli</option>
                                        <option value="3" <?php echo (isset($_GET['branch']) && $_GET['branch'] === '3') ? 'selected' : ''; ?>>Kasaragod</option>
                                        <option value="4" <?php echo (isset($_GET['branch']) && $_GET['branch'] === '4') ? 'selected' : ''; ?>>Kochi</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="category_filter" class="form-label">Category</label>
                                    <select class="form-select" id="category_filter" name="category">
                                        <option value="">All Categories</option>
                                        <option value="Salary" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Salary') ? 'selected' : ''; ?>>Salary</option>
                                        <option value="Rent" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Rent') ? 'selected' : ''; ?>>Rent</option>
                                        <option value="Water" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Water') ? 'selected' : ''; ?>>Water</option>
                                        <option value="Equipment Purchases" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Equipment Purchases') ? 'selected' : ''; ?>>Equipment Purchases</option>
                                        <option value="Stationery" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Stationery') ? 'selected' : ''; ?>>Stationery</option>
                                        <option value="Fees" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Fees') ? 'selected' : ''; ?>>Fees</option>
                                        <option value="Special Training Fees" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Special Training Fees') ? 'selected' : ''; ?>>Special Training Fees</option>
                                        <option value="School Training" <?php echo (isset($_GET['category']) && $_GET['category'] === 'School Training') ? 'selected' : ''; ?>>School Training</option>
                                        <option value="Community Training" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Community Training') ? 'selected' : ''; ?>>Community Training</option>
                                        <option value="Other" <?php echo (isset($_GET['category']) && $_GET['category'] === 'Other') ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo isset($_GET['start_date']) ? htmlspecialchars($_GET['start_date']) : ''; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo isset($_GET['end_date']) ? htmlspecialchars($_GET['end_date']) : ''; ?>">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>Filter
                                    </button>
                                    <a href="transactions.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Clear
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">All Transactions</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th class="important-column">Description</th>
                                        <th>Type</th>
                                        <th>Category</th>
                                        <th class="important-column">Amount</th>
                                        <th>Branch</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(empty($transactions)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">No transactions found</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('M d, Y', strtotime($transaction['date'] ?? date('Y-m-d'))); ?></td>
                                        <td class="important-column"><?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?></td>
                                        <td>
                                            <span class="transaction-type <?php echo $transaction['type'] ?? 'unknown'; ?>">
                                                <?php echo ucfirst($transaction['type'] ?? 'Unknown'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($transaction['category'] ?? 'Uncategorized'); ?></span>
                                        </td>
                                        <td class="important-column">
                                            <span class="fw-semibold amount-<?php echo $transaction['type'] === 'income' ? 'income' : 'expense'; ?>">
                                                ₹<?php echo number_format($transaction['amount'] ?? 0, 2); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['branch_name'] ?? 'Unknown Branch'); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="editTransaction(<?php echo $transaction['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-delete" 
                                                        onclick="deleteTransaction(<?php echo $transaction['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Transaction Modal -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <?php 
                        if ($fromDashboard) {
                            echo 'Add Main Transaction';
                        } elseif ($_SESSION['role'] === 'admin') {
                            echo 'Add Transaction (Select Branch)';
                        } else {
                            $branchName = $_SESSION['branch_name'] ?? 'Your Branch';
                            echo 'Add Transaction - ' . htmlspecialchars($branchName);
                        }
                        ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addTransactionForm" method="POST" action="api/add_transaction.php">
                    <div class="modal-body">
                        <?php if ($fromDashboard): ?>
                        <!-- Main dashboard always creates main transactions (NULL branch_id) -->
                        <input type="hidden" id="branch_id" name="branch_id" value="">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Main Transaction:</strong> This transaction will be added to the main account.
                        </div>
                        <?php else: ?>
                        <!-- All other cases: auto-set branch based on user's location -->
                        <input type="hidden" id="branch_id" name="branch_id" value="<?php echo $_SESSION['branch_id'] ?? ''; ?>">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Branch Transaction:</strong> This transaction will be added to your branch.
                        </div>
                        <?php endif; ?>
                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <!-- Expense Categories -->
                                <option value="Salary" data-type="expense">Salary</option>
                                <option value="Rent" data-type="expense">Rent</option>
                                <option value="Water" data-type="expense">Water</option>
                                <option value="Equipment Purchases" data-type="expense">Equipment Purchases</option>
                                <option value="Stationery" data-type="expense">Stationery</option>
                                <option value="Other" data-type="expense">Other (Expense)</option>
                                <!-- Income Categories -->
                                <option value="Fees" data-type="income">Fees</option>
                                <option value="Special Training Fees" data-type="income">Special Training Fees</option>
                                <option value="School Training" data-type="income">School Training</option>
                                <option value="Community Training" data-type="income">Community Training</option>
                                <option value="Other" data-type="income">Other (Income)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">Select Type</option>
                                <option value="expense">Expense</option>
                                <option value="income">Income</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Type will be automatically selected based on category
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">Amount</label>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Transaction</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Transaction Modal -->
    <div class="modal fade" id="editTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Transaction</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editTransactionForm" method="POST" action="api/update_transaction.php">
                    <input type="hidden" id="edit_transaction_id" name="transaction_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_branch_id" class="form-label">Branch</label>
                            <select class="form-select" id="edit_branch_id" name="branch_id" required>
                                <option value="">Select Branch</option>
                                <option value="1">Uppala</option>
                                <option value="2">Seethangoli</option>
                                <option value="3">Kasaragod</option>
                                <option value="4">Kochi</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_amount" class="form-label">Amount</label>
                            <input type="number" class="form-control" id="edit_amount" name="amount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit_category" class="form-label">Category</label>
                            <select class="form-select" id="edit_category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Office Supplies">Office Supplies</option>
                                <option value="Utilities">Utilities</option>
                                <option value="Maintenance">Maintenance</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="edit_date" name="date" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Transaction</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light bg-white d-lg-none border-top">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link active">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branches.php" class="nav-link">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branches</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // Set today's date as default
        document.getElementById('date').valueAsDate = new Date();
        // Duplicate event listener removed - handled below
        
        // Handle add transaction form submission
        document.getElementById('addTransactionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Ensure type is selected if category is selected
            const category = document.getElementById('category').value;
            const type = document.getElementById('type').value;
            
            if (category && !type) {
                alert('Please select a category first to auto-select the transaction type.');
                return;
            }
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Adding...';
            
            fetch('api/add_transaction.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessNotification('Transaction added successfully!');
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addTransactionModal'));
                    modal.hide();
                    // Reset form
                    this.reset();
                    // Reload page to show new transaction
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showErrorNotification(data.message || 'Failed to add transaction');
                }
            })
            .catch(error => {
                showErrorNotification('Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
        
        // Handle edit transaction form submission
        document.getElementById('editTransactionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Updating...';
            
            fetch('api/update_transaction.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessNotification('Transaction updated successfully!');
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editTransactionModal'));
                    modal.hide();
                    // Reload page to show updated transaction
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showErrorNotification(data.message || 'Failed to update transaction');
                }
            })
            .catch(error => {
                showErrorNotification('Network error occurred');
                console.error('Error:', error);
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
        
        // Edit transaction function
        function editTransaction(id) {
            // Fetch transaction data and populate modal
            fetch(`api/get_transaction.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('edit_transaction_id').value = data.data.id;
                        document.getElementById('edit_branch_id').value = data.data.branch_id;
                        document.getElementById('edit_amount').value = data.data.amount;
                        document.getElementById('edit_description').value = data.data.description;
                        document.getElementById('edit_category').value = data.data.category;
                        document.getElementById('edit_date').value = data.data.date;
                        
                        new bootstrap.Modal(document.getElementById('editTransactionModal')).show();
                    } else {
                        showErrorNotification('Failed to load transaction data');
                    }
                })
                .catch(error => {
                    showErrorNotification('Network error occurred');
                    console.error('Error:', error);
                });
        }
        
        // Delete transaction function
        function deleteTransaction(id) {
            if (confirm('Are you sure you want to delete this transaction?')) {
                fetch(`api/delete_transaction.php?id=${id}`, {method: 'POST'})
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessNotification('Transaction deleted successfully!');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showErrorNotification(data.message || 'Failed to delete transaction');
                        }
                    })
                    .catch(error => {
                        showErrorNotification('Network error occurred');
                        console.error('Error:', error);
                    });
            }
        }
        
        // Notification functions
        function showSuccessNotification(message) {
            showNotification(message, 'success');
        }
        
        function showErrorNotification(message) {
            showNotification(message, 'danger');
        }
        
        function showNotification(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertId = 'alert-' + Date.now();
            
            const alert = document.createElement('div');
            alert.id = alertId;
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.appendChild(alert);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, 5000);
        }
        
        // Auto-select income/expense based on category selection
        document.getElementById('category').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const dataType = selectedOption.getAttribute('data-type');
            const typeSelect = document.getElementById('type');
            
            // Auto-select type based on category
            if (dataType) {
                typeSelect.value = dataType;
                typeSelect.classList.add('bg-light');
                
                // Disable all options except the selected one
                for (let option of typeSelect.options) {
                    option.disabled = option.value !== dataType && option.value !== '';
                }
            } else {
                typeSelect.value = '';
                typeSelect.classList.remove('bg-light');
                
                // Re-enable all options
                for (let option of typeSelect.options) {
                    option.disabled = false;
                }
            }
        });
        
        // Auto-open modal if coming from dashboard
        <?php if ($fromDashboard): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = new bootstrap.Modal(document.getElementById('addTransactionModal'));
            modal.show();
            
            // Remove source parameter from URL to prevent modal reopening on page refresh
            if (window.history.replaceState) {
                const url = new URL(window.location);
                url.searchParams.delete('source');
                window.history.replaceState({}, document.title, url);
            }
        });
        <?php endif; ?>
        
        // Handle filter collapse chevron rotation
        document.getElementById('filterCollapse').addEventListener('show.bs.collapse', function () {
            const chevron = document.querySelector('[data-bs-target="#filterCollapse"] i.fa-chevron-down');
            chevron.style.transform = 'rotate(180deg)';
        });
        
        document.getElementById('filterCollapse').addEventListener('hide.bs.collapse', function () {
            const chevron = document.querySelector('[data-bs-target="#filterCollapse"] i.fa-chevron-down');
            chevron.style.transform = 'rotate(0deg)';
        });
    </script>
</body>
</html>
