<?php
require_once 'classes/User.php';

echo "<h2>Fix User Names</h2>";

try {
    $user = new User();
    
    // Get all users
    $allUsers = $user->getAllUsers();
    
    echo "<h3>Checking User Names</h3>";
    
    $fixedUsers = [];
    
    foreach ($allUsers as $userData) {
        $needsUpdate = false;
        $newFirstName = $userData['first_name'];
        $newLastName = $userData['last_name'];
        
        // Check if first_name is empty or null
        if (empty($userData['first_name'])) {
            $newFirstName = 'User';
            $needsUpdate = true;
        }
        
        // Check if last_name is empty or null
        if (empty($userData['last_name'])) {
            $newLastName = 'Name';
            $needsUpdate = true;
        }
        
        if ($needsUpdate) {
            echo "<p>Updating user: {$userData['username']} - Setting first_name to '{$newFirstName}', last_name to '{$newLastName}'</p>";
            
            // Update the user
            $result = $user->updateUser(
                $userData['id'],
                $userData['username'],
                $userData['email'],
                $userData['role'],
                $userData['branch_id'],
                $newFirstName,
                $newLastName,
                $userData['phone'] ?? null
            );
            
            if ($result) {
                echo "<p style='color: green;'>✓ Updated successfully</p>";
                $fixedUsers[] = $userData['username'];
            } else {
                echo "<p style='color: red;'>✗ Failed to update</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ User {$userData['username']} already has proper names</p>";
        }
    }
    
    if (!empty($fixedUsers)) {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: green; margin-top: 0;'>✅ Fixed Users</h4>";
        echo "<p>The following users have been updated with proper names:</p>";
        echo "<ul>";
        foreach ($fixedUsers as $username) {
            echo "<li>{$username}</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: blue; margin-top: 0;'>ℹ️ All Users Have Proper Names</h4>";
        echo "<p>No users needed to be updated. All users already have first_name and last_name values.</p>";
        echo "</div>";
    }
    
    // Show current user status
    echo "<hr>";
    echo "<h3>Current User Status</h3>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped table-bordered'>";
    echo "<thead class='table-dark'>";
    echo "<tr><th>Username</th><th>First Name</th><th>Last Name</th><th>Role</th><th>Branch</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($allUsers as $userData) {
        echo "<tr>";
        echo "<td><strong>{$userData['username']}</strong></td>";
        echo "<td>{$userData['first_name']}</td>";
        echo "<td>{$userData['last_name']}</td>";
        echo "<td><span class='badge bg-" . ($userData['role'] === 'admin' ? 'danger' : ($userData['role'] === 'branch_manager' ? 'warning' : 'info')) . "'>" . ucfirst(str_replace('_', ' ', $userData['role'])) . "</span></td>";
        echo "<td>" . ($userData['branch_name'] ?? 'None') . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='branch_dashboard.php?branch=1' style='background: #22c55e; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Branch Dashboard</a>";
echo "<a href='branch_login.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Branch Login</a>";
echo "<a href='login.php' style='background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Admin Login</a>";
echo "</div>";
?>
