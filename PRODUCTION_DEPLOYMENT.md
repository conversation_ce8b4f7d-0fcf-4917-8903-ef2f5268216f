# Production Deployment Guide

## 🚀 Deploying to online.mycloudforge.com

This guide will help you deploy the Expense Management System to your production server at `https://online.mycloudforge.com`.

## 📋 Prerequisites

- Ubuntu 20.04+ or CentOS 8+
- Apache 2.4+
- PHP 8.1+
- MySQL 8.0+
- SSL Certificate (Let's Encrypt)
- Root access to server

## 🔧 Quick Deployment

### 1. Upload Files
```bash
# Upload all files to /var/www/html/new_expns_app/
scp -r * <EMAIL>:/var/www/html/new_expns_app/
```

### 2. Run Deployment Script
```bash
# SSH into your server
ssh <EMAIL>

# Navigate to application directory
cd /var/www/html/new_expns_app/

# Make deployment script executable
chmod +x deploy_production.sh

# Run deployment script
sudo ./deploy_production.sh
```

### 3. Configure Database
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE expense_management;"

# Import schema
mysql -u root -p expense_management < database/schema.sql

# Update production config
nano config/production_config.php
```

## ⚙️ Manual Configuration

### Database Setup
1. **Create Database:**
   ```sql
   CREATE DATABASE expense_management;
   CREATE USER 'expense_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON expense_management.* TO 'expense_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Import Schema:**
   ```bash
   mysql -u expense_user -p expense_management < database/schema.sql
   ```

### Apache Configuration
1. **Enable Required Modules:**
   ```bash
   sudo a2enmod rewrite
   sudo a2enmod headers
   sudo a2enmod deflate
   sudo a2enmod expires
   sudo a2enmod ssl
   ```

2. **Create Virtual Host:**
   ```apache
   <VirtualHost *:80>
       ServerName online.mycloudforge.com
       DocumentRoot /var/www/html/new_expns_app
       
       <Directory /var/www/html/new_expns_app>
           AllowOverride All
           Require all granted
       </Directory>
       
       # Redirect to HTTPS
       RewriteEngine On
       RewriteCond %{HTTPS} off
       RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   </VirtualHost>
   
   <VirtualHost *:443>
       ServerName online.mycloudforge.com
       DocumentRoot /var/www/html/new_expns_app
       
       SSLEngine on
       SSLCertificateFile /etc/letsencrypt/live/online.mycloudforge.com/fullchain.pem
       SSLCertificateKeyFile /etc/letsencrypt/live/online.mycloudforge.com/privkey.pem
       
       <Directory /var/www/html/new_expns_app>
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```

### SSL Certificate
```bash
# Install Certbot
sudo apt install certbot python3-certbot-apache

# Get SSL certificate
sudo certbot --apache -d online.mycloudforge.com
```

### PHP Configuration
```ini
; /etc/php/8.1/apache2/conf.d/99-expense-management.ini
memory_limit = 256M
max_execution_time = 60
upload_max_filesize = 20M
post_max_size = 20M

; Security
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Error reporting
display_errors = Off
log_errors = On
error_log = /var/www/html/new_expns_app/logs/php_errors.log
```

## 🔒 Security Configuration

### File Permissions
```bash
# Set proper ownership
sudo chown -R www-data:www-data /var/www/html/new_expns_app/

# Set directory permissions
sudo find /var/www/html/new_expns_app/ -type d -exec chmod 755 {} \;

# Set file permissions
sudo find /var/www/html/new_expns_app/ -type f -exec chmod 644 {} \;

# Set writable directories
sudo chmod -R 777 /var/www/html/new_expns_app/backups/
sudo chmod -R 777 /var/www/html/new_expns_app/logs/
sudo chmod -R 777 /var/www/html/new_expns_app/cache/
```

### Firewall Configuration
```bash
# Configure UFW
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable
```

## 📊 Backup System

### Automated Backups
The system includes automated backup functionality:

1. **Database Backups:** Daily at 2 AM
2. **Application Backups:** Weekly
3. **Retention:** 30 days
4. **Location:** `/var/backups/expense_management/`

### Manual Backup
```bash
# Create database backup
mysqldump -u expense_user -p expense_management > backup_$(date +%Y%m%d).sql

# Create application backup
tar -czf app_backup_$(date +%Y%m%d).tar.gz /var/www/html/new_expns_app/
```

### Backup Management
Access the backup manager at: `https://online.mycloudforge.com/new_expns_app/backup_manager.php`

## 📈 Monitoring

### Health Check
```bash
# Check application health
curl https://online.mycloudforge.com/new_expns_app/?health

# Check database connection
mysql -u expense_user -p -e "SELECT 1"
```

### Log Monitoring
```bash
# Application logs
tail -f /var/www/html/new_expns_app/logs/app.log

# Error logs
tail -f /var/www/html/new_expns_app/logs/error.log

# Apache logs
tail -f /var/log/apache2/expense_management_error.log
```

### Performance Monitoring
```bash
# Check disk usage
df -h

# Check memory usage
free -h

# Check Apache status
sudo systemctl status apache2
```

## 🔧 Maintenance

### Regular Tasks
1. **Daily:** Check logs for errors
2. **Weekly:** Review backup status
3. **Monthly:** Update dependencies
4. **Quarterly:** Security audit

### Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update application
cd /var/www/html/new_expns_app/
git pull origin main

# Clear cache
sudo rm -rf cache/*
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed:**
   ```bash
   # Check MySQL status
   sudo systemctl status mysql
   
   # Test connection
   mysql -u expense_user -p -e "SELECT 1"
   ```

2. **Permission Denied:**
   ```bash
   # Fix permissions
   sudo chown -R www-data:www-data /var/www/html/new_expns_app/
   sudo chmod -R 755 /var/www/html/new_expns_app/
   ```

3. **SSL Certificate Issues:**
   ```bash
   # Renew certificate
   sudo certbot renew
   
   # Check certificate status
   sudo certbot certificates
   ```

4. **Application Not Loading:**
   ```bash
   # Check Apache error logs
   sudo tail -f /var/log/apache2/error.log
   
   # Check PHP errors
   tail -f /var/www/html/new_expns_app/logs/php_errors.log
   ```

## 📞 Support

### Log Files Location
- **Application Logs:** `/var/www/html/new_expns_app/logs/`
- **Apache Logs:** `/var/log/apache2/`
- **MySQL Logs:** `/var/log/mysql/`
- **System Logs:** `/var/log/syslog`

### Configuration Files
- **Apache Config:** `/etc/apache2/sites-available/expense-management.conf`
- **PHP Config:** `/etc/php/8.1/apache2/conf.d/99-expense-management.ini`
- **App Config:** `/var/www/html/new_expns_app/config/production_config.php`

## 🎯 Post-Deployment Checklist

- [ ] SSL certificate installed and working
- [ ] Database connection tested
- [ ] All application features working
- [ ] Backup system configured
- [ ] Monitoring set up
- [ ] Security headers enabled
- [ ] Performance optimized
- [ ] Log rotation configured

## 🌐 Access URLs

- **Main Application:** https://online.mycloudforge.com/new_expns_app/
- **Backup Manager:** https://online.mycloudforge.com/new_expns_app/backup_manager.php
- **Health Check:** https://online.mycloudforge.com/new_expns_app/?health

Your Expense Management System is now ready for production use! 🚀


