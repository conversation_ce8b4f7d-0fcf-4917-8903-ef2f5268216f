<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'classes/Dashboard.php';
require_once 'classes/Transaction.php';
require_once 'classes/Student.php';
require_once 'classes/Enquiry.php';
require_once 'classes/Branch.php';
require_once 'classes/User.php';

$dashboard = new Dashboard();
$transaction = new Transaction();
$student = new Student();
$enquiry = new Enquiry();
$branch = new Branch();
$user = new User();

// Get branch data from database
$branches = $branch->getAllBranches();

// Get statistics for each branch
$branch_stats = [];
foreach($branches as $branch) {
    $expenses = $transaction->getTotalExpensesByBranch($branch['id']);
    $students = $student->getStudentsByBranch($branch['id']);
    $enquiries = $enquiry->getEnquiriesByBranch($branch['id']);
    $attendance_stats = $student->getAttendanceStats($branch['id']);
    
    $branch_stats[] = [
        'id' => $branch['id'],
        'name' => $branch['name'],
        'location' => $branch['location'],
        'expenses' => $expenses,
        'student_count' => count($students),
        'enquiry_count' => count($enquiries),
        'attendance_rate' => $attendance_stats['total_students'] > 0 ? 
            round(($attendance_stats['present_count'] / $attendance_stats['total_students']) * 100, 1) : 0
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Management - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand premium-brand" href="index.php">
                <div class="brand-text">
                    <span class="brand-main">KFT Manager</span>
                </div>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php" title="Dashboard">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php" title="Transactions">
                            <i class="fas fa-receipt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="branches.php" title="Branches">
                            <i class="fas fa-building"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php" title="Settings">
                            <i class="fas fa-cog"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-container" style="margin-top: 85px;">
        <!-- Page Header -->
        <div class="row mb-3">
            <div class="col-12">
                <h1 class="h3 mb-0">Branch Management</h1>
                <p class="text-muted">Overview of all branches and their performance</p>
            </div>
        </div>

        <!-- Branch Cards -->
        <div class="row mb-4">
            <?php foreach($branch_stats as $branch): ?>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card branch-card shadow h-100" onclick="viewBranch(<?php echo $branch['id']; ?>)">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    <?php echo $branch['name']; ?> Branch</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo $branch['location']; ?>
                                </div>
                                <div class="text-xs text-muted mt-1">
                                    <i class="fas fa-rupee-sign me-1"></i>₹<?php echo number_format($branch['expenses'], 2); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <div class="text-xs text-muted">Students</div>
                                <div class="h6 mb-0"><?php echo $branch['student_count']; ?></div>
                            </div>
                            <div class="col-6">
                                <div class="text-xs text-muted">Enquiries</div>
                                <div class="h6 mb-0"><?php echo $branch['enquiry_count']; ?></div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="text-xs text-muted">Attendance Rate</div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: <?php echo $branch['attendance_rate']; ?>%">
                                </div>
                            </div>
                            <small class="text-muted"><?php echo $branch['attendance_rate']; ?>%</small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <a href="branch_dashboard.php?branch=<?php echo $branch['id']; ?>" 
                           class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-arrow-right me-2"></i>View Details
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Branch Management Section -->
        <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-building me-2"></i>Branch Management
                            </h6>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addBranchModal">
                                <i class="fas fa-plus me-2"></i>Add New Branch
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Branch Name</th>
                                        <th>Location</th>
                                        <th>Users</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($branches as $branch): ?>
                                    <?php 
                                    $branchUsers = $user->getUsersByBranch($branch['id']);
                                    $userCount = count($branchUsers);
                                    ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="branch-icon me-2">
                                                    <i class="fas fa-building text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold"><?php echo htmlspecialchars($branch['name']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($branch['location']); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $userCount; ?> users</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">Active</span>
                                        </td>
                                        <td><?php echo date('M d, Y', strtotime($branch['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="editBranch(<?php echo $branch['id']; ?>)" title="Edit Branch">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="manageUsers(<?php echo $branch['id']; ?>)" title="Manage Users">
                                                    <i class="fas fa-users"></i>
                                                </button>
                                                <a href="login.php" class="btn btn-sm btn-outline-success" title="Branch Login">
                                                    <i class="fas fa-sign-in-alt"></i>
                                                </a>
                                                <?php if ($userCount == 0): ?>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteBranch(<?php echo $branch['id']; ?>)" title="Delete Branch">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- Non-Admin User Message -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-lock fa-3x text-muted"></i>
                        </div>
                        <h5 class="text-muted">Branch Management Access Restricted</h5>
                        <p class="text-muted mb-3">
                            Branch management features are only available to admin users. 
                            <?php if (!isset($_SESSION['role'])): ?>
                            Please log in with an admin account to access these features.
                            <?php else: ?>
                            Your current role: <strong><?php echo htmlspecialchars($_SESSION['role']); ?></strong>
                            <?php endif; ?>
                        </p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="check_admin.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-shield me-2"></i>Check Admin Status
                            </a>
                            <a href="login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Login as Admin
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Default Admin Credentials:</strong><br>
                                Username: <code>admin</code><br>
                                Password: <code>admin123</code>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Branch Comparison Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Branch Performance Comparison</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Branch</th>
                                        <th>Total Expenses</th>
                                        <th>Students</th>
                                        <th>Enquiries</th>
                                        <th>Attendance Rate</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($branch_stats as $branch): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $branch['name']; ?></strong>
                                            <br><small class="text-muted"><?php echo $branch['location']; ?></small>
                                        </td>
                                        <td class="fw-bold">₹<?php echo number_format($branch['expenses'], 2); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $branch['student_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo $branch['enquiry_count']; ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-<?php 
                                                        echo $branch['attendance_rate'] >= 80 ? 'success' : 
                                                            ($branch['attendance_rate'] >= 60 ? 'warning' : 'danger'); 
                                                    ?>" style="width: <?php echo $branch['attendance_rate']; ?>%">
                                                    </div>
                                                </div>
                                                <span><?php echo $branch['attendance_rate']; ?>%</span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php 
                                            $performance = 'Good';
                                            $badge_class = 'success';
                                            if ($branch['attendance_rate'] < 60) {
                                                $performance = 'Poor';
                                                $badge_class = 'danger';
                                            } elseif ($branch['attendance_rate'] < 80) {
                                                $performance = 'Average';
                                                $badge_class = 'warning';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $badge_class; ?>"><?php echo $performance; ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="branch_dashboard.php?branch=<?php echo $branch['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewReports(<?php echo $branch['id']; ?>)">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="manageBranch(<?php echo $branch['id']; ?>)">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats section removed for cleaner design -->
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="navbar fixed-bottom navbar-light bg-white d-lg-none border-top">
        <div class="container-fluid">
            <div class="row w-100">
                <div class="col-3 text-center">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        <small class="d-block">Dashboard</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <small class="d-block">Transactions</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="branches.php" class="nav-link active">
                        <i class="fas fa-building"></i>
                        <small class="d-block">Branches</small>
                    </a>
                </div>
                <div class="col-3 text-center">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <small class="d-block">Settings</small>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Add Branch Modal -->
    <div class="modal fade" id="addBranchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Branch</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addBranchForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="branch_name" class="form-label">Branch Name</label>
                            <input type="text" class="form-control" id="branch_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="branch_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="branch_location" name="location" required>
                        </div>
                        <div class="mb-3">
                            <label for="manager_name" class="form-label">Manager Name</label>
                            <input type="text" class="form-control" id="manager_name" name="manager_name">
                        </div>
                        <div class="mb-3">
                            <label for="branch_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="branch_phone" name="phone">
                        </div>
                        <div class="mb-3">
                            <label for="branch_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="branch_email" name="email">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Branch</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Branch Modal -->
    <div class="modal fade" id="editBranchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Branch</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editBranchForm">
                    <input type="hidden" id="edit_branch_id" name="branch_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_branch_name" class="form-label">Branch Name</label>
                            <input type="text" class="form-control" id="edit_branch_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_branch_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="edit_branch_location" name="location" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_manager_name" class="form-label">Manager Name</label>
                            <input type="text" class="form-control" id="edit_manager_name" name="manager_name">
                        </div>
                        <div class="mb-3">
                            <label for="edit_branch_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="edit_branch_phone" name="phone">
                        </div>
                        <div class="mb-3">
                            <label for="edit_branch_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_branch_email" name="email">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Branch</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Manage Users Modal -->
    <div class="modal fade" id="manageUsersModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Manage Branch Users</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="manage_branch_id" value="">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>Current Users</h6>
                        <button class="btn btn-primary btn-sm" onclick="addUserToBranch()" id="addUserBtn">
                            <i class="fas fa-plus me-1"></i>Add User
                        </button>
                    </div>
                    <div id="branch-users-list">
                        <!-- Users will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add User to Branch</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addUserForm">
                    <div class="modal-body">
                        <input type="hidden" id="user_branch_id" name="branch_id">
                        <input type="hidden" id="user_role" name="role" value="branch_staff">
                        <input type="hidden" id="user_first_name" name="first_name" value="User">
                        <input type="hidden" id="user_email" name="email" value="">
                        <input type="hidden" id="user_phone" name="phone" value="">
                        <div class="mb-3">
                            <label for="user_username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="user_username" name="username" required readonly>
                            <small class="text-muted">Auto-generated based on branch</small>
                        </div>
                        <div class="mb-3">
                            <label for="user_password" class="form-label">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="user_password" name="password" value="password123" required>
                                <button class="btn btn-outline-primary" type="button" onclick="generatePassword('user_password')" title="Generate New Password">
                                    <i class="fas fa-random"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('user_password')" title="Toggle Password Visibility">
                                    <i class="fas fa-eye" id="user_password_icon"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyPassword('user_password')" title="Copy Password">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <small class="text-muted">Click generate button for a new secure password</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Show Password Modal -->
    <div class="modal fade" id="showPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Username:</label>
                        <input type="text" class="form-control" id="show_username" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Password:</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="show_password" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('show_password')">
                                <i class="fas fa-eye" id="show_password_icon"></i>
                            </button>
                            <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('show_password')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit User Information</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editUserForm">
                    <div class="modal-body">
                        <input type="hidden" id="edit_user_id" name="user_id">
                        
                        <div class="mb-3">
                            <label class="form-label">Username:</label>
                            <input type="text" class="form-control" id="edit_username" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Name:</label>
                            <input type="text" class="form-control" id="edit_name" name="new_name" placeholder="Enter new name">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">New Password:</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="edit_password" name="new_password" placeholder="Enter new password">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('edit_password')">
                                    <i class="fas fa-eye" id="edit_password_icon"></i>
                                </button>
                                <button class="btn btn-outline-primary" type="button" onclick="generatePassword('edit_password')">
                                    <i class="fas fa-random"></i>
                                </button>
                            </div>
                            <small class="text-muted">Leave blank to keep current password</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function viewBranch(branchId) {
            window.location.href = `branch_dashboard.php?branch=${branchId}`;
        }
        
        function viewReports(branchId) {
            alert('Reports feature coming soon for branch ' + branchId);
        }
        
        function manageBranch(branchId) {
            alert('Branch management feature coming soon for branch ' + branchId);
        }

        // Add Branch Form
        document.getElementById('addBranchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('api/add_branch.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Branch added successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred while adding the branch.');
                console.error('Error:', error);
            });
        });

        // Edit Branch
        function editBranch(branchId) {
            fetch(`api/get_branch.php?id=${branchId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('edit_branch_id').value = data.data.id;
                        document.getElementById('edit_branch_name').value = data.data.name;
                        document.getElementById('edit_branch_location').value = data.data.location;
                        document.getElementById('edit_manager_name').value = data.data.manager_name || '';
                        document.getElementById('edit_branch_phone').value = data.data.phone || '';
                        document.getElementById('edit_branch_email').value = data.data.email || '';
                        
                        new bootstrap.Modal(document.getElementById('editBranchModal')).show();
                    } else {
                        alert('Failed to load branch data');
                    }
                })
                .catch(error => {
                    alert('An error occurred while loading branch data.');
                    console.error('Error:', error);
                });
        }

        // Edit Branch Form
        document.getElementById('editBranchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('api/update_branch.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Branch updated successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred while updating the branch.');
                console.error('Error:', error);
            });
        });

        // Delete Branch
        function deleteBranch(branchId) {
            if (confirm('Are you sure you want to delete this branch? This action cannot be undone.')) {
                const formData = new FormData();
                formData.append('id', branchId);
                
                fetch('api/delete_branch.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Branch deleted successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('An error occurred while deleting the branch.');
                    console.error('Error:', error);
                });
            }
        }

        // Manage Users
        function manageUsers(branchId, branchName = null) {
            // Store the branch ID in the hidden input
            document.getElementById('manage_branch_id').value = branchId;
            
            // Get branch name - either from parameter or from button click
            let actualBranchName = branchName;
            
            if (!actualBranchName && typeof event !== 'undefined' && event.target) {
                // Called from button click - get branch name from table row
                const clickedButton = event.target.closest('button');
                const branchRow = clickedButton.closest('tr');
                const branchNameCell = branchRow.querySelector('td:first-child');
                actualBranchName = branchNameCell ? branchNameCell.textContent.trim() : 'Branch';
            } else if (!actualBranchName) {
                // Fallback to stored branch name or default
                actualBranchName = document.getElementById('manage_branch_id').getAttribute('data-branch-name') || 'Branch';
            }
            
            // Store branch name in the hidden input for later use
            document.getElementById('manage_branch_id').setAttribute('data-branch-name', actualBranchName);
            
            fetch(`api/get_branch_users.php?branch_id=${branchId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const usersList = document.getElementById('branch-users-list');
                        usersList.innerHTML = '';
                        
                        if (data.users.length === 0) {
                            usersList.innerHTML = '<p class="text-muted">No users assigned to this branch.</p>';
                        } else {
                            data.users.forEach(user => {
                                const userRow = document.createElement('div');
                                userRow.className = 'd-flex justify-content-between align-items-center border-bottom py-2';
                                userRow.innerHTML = `
                                    <div>
                                        <strong>${user.first_name} ${user.last_name}</strong>
                                        <br><small class="text-muted">${user.username} - ${user.role.replace('_', ' ')}</small>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-info" onclick="showUserPassword(${user.id}, '${user.username}')" title="Show Password">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="editUserInfo(${user.id}, '${user.first_name}', '${user.username}')" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="removeUserFromBranch(${user.id})" title="Remove User">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                `;
                                usersList.appendChild(userRow);
                            });
                        }
                        
                        new bootstrap.Modal(document.getElementById('manageUsersModal')).show();
                    } else {
                        alert('Failed to load users');
                    }
                })
                .catch(error => {
                    alert('An error occurred while loading users.');
                    console.error('Error:', error);
                });
        }

        // Add User to Branch
        function addUserToBranch() {
            const branchId = document.getElementById('manage_branch_id').value;
            
            if (!branchId) {
                alert('Error: Branch ID not found. Please try again.');
                return;
            }
            
            // Get stored branch name
            const branchName = document.getElementById('manage_branch_id').getAttribute('data-branch-name') || 'branch';
            
            document.getElementById('user_branch_id').value = branchId;
            document.getElementById('user_branch_id').setAttribute('data-branch-name', branchName);
            
            // Clear and reset form
            document.getElementById('addUserForm').reset();
            document.getElementById('user_branch_id').value = branchId;
            document.getElementById('user_branch_id').setAttribute('data-branch-name', branchName);
            document.getElementById('user_password').value = 'password123';
            document.getElementById('user_role').value = 'branch_staff';
            document.getElementById('user_first_name').value = 'User';
            document.getElementById('user_email').value = '';
            document.getElementById('user_phone').value = '';
            
            // Auto-generate username
            generateUsername();
            
            new bootstrap.Modal(document.getElementById('addUserModal')).show();
        }
        
        // Auto-generate username based on branch
        function generateUsername() {
            // Get stored branch name
            const branchName = document.getElementById('user_branch_id').getAttribute('data-branch-name') || 'branch';
            const cleanBranchName = branchName.toLowerCase().replace(/\s+/g, '');
            
            // Generate unique username: user.branchname.timestamp
            const timestamp = Date.now().toString().slice(-4); // Last 4 digits of timestamp
            const username = `user.${cleanBranchName}.${timestamp}`;
            document.getElementById('user_username').value = username;
        }
        
        // Toggle password visibility
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + '_icon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        // Generate secure password
        function generatePassword(inputId) {
            const input = document.getElementById(inputId);
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            
            // Generate 8-character password
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            input.value = password;
            
            // Show temporary success message
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-success"></i>';
            setTimeout(() => {
                button.innerHTML = originalHTML;
            }, 1000);
        }
        
        // Copy password to clipboard
        function copyPassword(inputId) {
            const input = document.getElementById(inputId);
            input.select();
            input.setSelectionRange(0, 99999); // For mobile devices
            
            try {
                document.execCommand('copy');
                // Show temporary success message
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check text-success"></i>';
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                }, 1000);
            } catch (err) {
                // Fallback for modern browsers
                navigator.clipboard.writeText(input.value).then(() => {
                    const button = event.target.closest('button');
                    const originalHTML = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check text-success"></i>';
                    setTimeout(() => {
                        button.innerHTML = originalHTML;
                    }, 1000);
                });
            }
        }

        // Username is auto-generated when modal opens, no event listeners needed

        // Handle Add User Form Submission
        document.getElementById('addUserForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Debug: Log form data
            console.log('Form data being sent:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
            submitBtn.disabled = true;
            
            fetch('api/add_user.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text(); // Get raw response first
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        alert('User added successfully');
                        bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                        this.reset();
                        // Refresh the users list
                        const branchId = document.getElementById('user_branch_id').value;
                        const branchName = document.getElementById('user_branch_id').getAttribute('data-branch-name');
                        manageUsers(branchId, branchName);
                    } else {
                        alert('Error: ' + data.message);
                        console.error('API Error:', data);
                    }
                } catch (e) {
                    console.error('JSON Parse Error:', e);
                    console.error('Raw response that failed to parse:', text);
                    alert('Error parsing server response. Check console for details.');
                }
            })
            .catch(error => {
                console.error('Network Error:', error);
                alert('Network error occurred: ' + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Edit User Form Submission
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            submitBtn.disabled = true;
            
            const formData = new FormData(this);
            
            fetch('api/update_user.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User updated successfully');
                    bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                    this.reset();
                    
                    // Refresh the users list
                    const branchId = document.getElementById('manage_branch_id').value;
                    const branchName = document.getElementById('manage_branch_id').getAttribute('data-branch-name');
                    manageUsers(branchId, branchName);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating user');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Remove User from Branch
        function removeUserFromBranch(userId) {
            if (confirm('Are you sure you want to remove this user from the branch?')) {
                fetch('api/delete_user.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: userId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('User removed successfully');
                        // Refresh the users list
                        const branchId = document.getElementById('manage_branch_id').value;
                        const branchName = document.getElementById('manage_branch_id').getAttribute('data-branch-name');
                        manageUsers(branchId, branchName);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing user');
                });
            }
        }

        // Show User Password
        function showUserPassword(userId, username) {
            // For security, we'll show a placeholder or generate a temporary password
            // In a real application, you might want to store passwords in a retrievable way
            // or implement a password reset system instead
            
            document.getElementById('show_username').value = username;
            document.getElementById('show_password').value = '*****'; // Placeholder
            
            // Show the modal
            new bootstrap.Modal(document.getElementById('showPasswordModal')).show();
        }

        // Edit User Information
        function editUserInfo(userId, currentName, username) {
            document.getElementById('edit_user_id').value = userId;
            document.getElementById('edit_username').value = username;
            document.getElementById('edit_name').value = currentName;
            document.getElementById('edit_password').value = '';
            
            // Show the modal
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }

        // Toggle Password Visibility
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + '_icon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Copy to Clipboard
        function copyToClipboard(inputId) {
            const input = document.getElementById(inputId);
            input.select();
            input.setSelectionRange(0, 99999); // For mobile devices
            
            try {
                document.execCommand('copy');
                alert('Copied to clipboard!');
            } catch (err) {
                // Fallback for modern browsers
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(input.value).then(() => {
                        alert('Copied to clipboard!');
                    });
                } else {
                    alert('Copy failed');
                }
            }
        }
    </script>
</body>
</html>
