<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: branch_login.php');
    exit;
}

// Check if user has permission to manage users (branch_manager or admin)
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'branch_manager' && $_SESSION['role'] !== 'admin')) {
    header('Location: branch_dashboard.php?branch=' . $_SESSION['branch_id']);
    exit;
}

require_once 'classes/User.php';
require_once 'classes/Branch.php';

$user = new User();
$branch = new Branch();

$current_branch_id = $_SESSION['branch_id'];
$current_branch = $branch->getBranchById($current_branch_id);
$branch_users = $user->getUsersByBranch($current_branch_id);

$message = '';
$message_type = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_user':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = $_POST['password'];
                $first_name = trim($_POST['first_name']);
                $last_name = trim($_POST['last_name']);
                $role = $_POST['role'];
                $phone = trim($_POST['phone']);
                
                // Check if username already exists
                if ($user->usernameExists($username)) {
                    $message = 'Username already exists. Please choose a different username.';
                    $message_type = 'danger';
                } else {
                    // Create new user
                    $result = $user->createUser(
                        $username,
                        $email,
                        $password,
                        $role,
                        $current_branch_id,
                        $first_name,
                        $last_name,
                        $phone
                    );
                    
                    if ($result) {
                        $message = 'User created successfully!';
                        $message_type = 'success';
                        $branch_users = $user->getUsersByBranch($current_branch_id); // Refresh list
                    } else {
                        $message = 'Failed to create user. Please try again.';
                        $message_type = 'danger';
                    }
                }
                break;
                
            case 'update_user':
                $user_id = $_POST['user_id'];
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $first_name = trim($_POST['first_name']);
                $last_name = trim($_POST['last_name']);
                $role = $_POST['role'];
                $phone = trim($_POST['phone']);
                
                // Check if username exists for other users
                if ($user->usernameExists($username, $user_id)) {
                    $message = 'Username already exists for another user.';
                    $message_type = 'danger';
                } else {
                    // Update user
                    $result = $user->updateUser(
                        $user_id,
                        $username,
                        $email,
                        $role,
                        $current_branch_id,
                        $first_name,
                        $last_name,
                        $phone
                    );
                    
                    if ($result) {
                        $message = 'User updated successfully!';
                        $message_type = 'success';
                        $branch_users = $user->getUsersByBranch($current_branch_id); // Refresh list
                    } else {
                        $message = 'Failed to update user.';
                        $message_type = 'danger';
                    }
                }
                break;
                
            case 'change_password':
                $user_id = $_POST['user_id'];
                $new_password = $_POST['new_password'];
                
                $result = $user->updatePassword($user_id, $new_password);
                
                if ($result) {
                    $message = 'Password updated successfully!';
                    $message_type = 'success';
                } else {
                    $message = 'Failed to update password.';
                    $message_type = 'danger';
                }
                break;
                
            case 'delete_user':
                $user_id = $_POST['user_id'];
                
                // Prevent deleting own account
                if ($user_id == $_SESSION['user_id']) {
                    $message = 'You cannot delete your own account.';
                    $message_type = 'danger';
                } else {
                    $result = $user->deleteUser($user_id);
                    
                    if ($result) {
                        $message = 'User deleted successfully!';
                        $message_type = 'success';
                        $branch_users = $user->getUsersByBranch($current_branch_id); // Refresh list
                    } else {
                        $message = 'Failed to delete user.';
                        $message_type = 'danger';
                    }
                }
                break;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo $current_branch['name']; ?> Branch</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand premium-brand" href="branch_dashboard.php?branch=<?php echo $current_branch_id; ?>">
                <div class="brand-text">
                    <span class="brand-main"><?php echo $current_branch['name']; ?> Branch</span>
                </div>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="branch_dashboard.php?branch=<?php echo $current_branch_id; ?>" title="Branch Dashboard">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="branch_user_management.php" title="User Management">
                            <i class="fas fa-users"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php" title="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-container" style="margin-top: 85px;">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-users text-primary me-2"></i>User Management
                        </h1>
                        <p class="text-muted">Manage users for <?php echo $current_branch['name']; ?> Branch</p>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-primary fs-6 mb-2"><?php echo ucfirst(str_replace('_', ' ', $_SESSION['role'] ?? 'User')); ?></div>
                        <div class="text-muted small">Branch: <?php echo $current_branch['name']; ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Alert -->
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- User Management Section -->
        <div class="row">
            <!-- Users List -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-users me-2"></i>Branch Users (<?php echo count($branch_users); ?>)
                        </h6>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createUserModal">
                            <i class="fas fa-plus me-2"></i>Add New User
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Username</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Phone</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($branch_users)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-users fa-3x mb-3 text-muted"></i><br>
                                            No users found for this branch.
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($branch_users as $user_data): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-2">
                                                    <i class="fas fa-user-circle fa-2x text-primary"></i>
                                                </div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($user_data['username']); ?></strong>
                                                    <?php if ($user_data['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge bg-info ms-2">You</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($user_data['first_name'] . ' ' . $user_data['last_name']); ?></td>
                                        <td><?php echo htmlspecialchars($user_data['email']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $user_data['role'] === 'branch_manager' ? 'warning' : 'info'; 
                                            ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $user_data['role'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($user_data['phone'] ?? 'N/A'); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo ($user_data['status'] ?? 'active') === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo ucfirst($user_data['status'] ?? 'active'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="editUser(<?php echo htmlspecialchars(json_encode($user_data)); ?>)" title="Edit User">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="changePassword(<?php echo $user_data['id']; ?>, '<?php echo htmlspecialchars($user_data['username']); ?>')" title="Change Password">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                <?php if ($user_data['id'] != $_SESSION['user_id']): ?>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(<?php echo $user_data['id']; ?>, '<?php echo htmlspecialchars($user_data['username']); ?>')" title="Delete User">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Branch Info -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-building me-2"></i>Branch Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Branch Name:</strong><br>
                            <span class="text-primary"><?php echo htmlspecialchars($current_branch['name']); ?></span>
                        </div>
                        <div class="mb-3">
                            <strong>Location:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($current_branch['location']); ?></span>
                        </div>
                        <div class="mb-3">
                            <strong>Total Users:</strong><br>
                            <span class="badge bg-primary fs-6"><?php echo count($branch_users); ?></span>
                        </div>
                        <div class="mb-3">
                            <strong>Managers:</strong><br>
                            <span class="badge bg-warning"><?php echo count(array_filter($branch_users, function($u) { return $u['role'] === 'branch_manager'; })); ?></span>
                        </div>
                        <div class="mb-3">
                            <strong>Staff:</strong><br>
                            <span class="badge bg-info"><?php echo count(array_filter($branch_users, function($u) { return $u['role'] === 'branch_staff'; })); ?></span>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <a href="branch_dashboard.php?branch=<?php echo $current_branch_id; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                <i class="fas fa-user-plus me-2"></i>Add New User
                            </button>
                            <button class="btn btn-outline-info" onclick="exportUsers()">
                                <i class="fas fa-download me-2"></i>Export Users
                            </button>
                            <button class="btn btn-outline-warning" onclick="resetAllPasswords()">
                                <i class="fas fa-key me-2"></i>Reset All Passwords
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Create New User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="create_user">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <small class="text-muted">Unique username for login</small>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="generatePassword('password')">
                                            <i class="fas fa-random"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password_icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="branch_staff">Branch Staff</option>
                                        <option value="branch_manager">Branch Manager</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit me-2"></i>Edit User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" id="edit_user_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="edit_username" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="edit_email" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="edit_first_name" name="first_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="edit_last_name" name="last_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_role" class="form-label">Role *</label>
                                    <select class="form-select" id="edit_role" name="role" required>
                                        <option value="branch_staff">Branch Staff</option>
                                        <option value="branch_manager">Branch Manager</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="edit_phone" name="phone">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="change_password">
                    <input type="hidden" name="user_id" id="password_user_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">User:</label>
                            <input type="text" class="form-control" id="password_username" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="generatePassword('new_password')">
                                    <i class="fas fa-random"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye" id="new_password_icon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Update Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>Delete User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" id="delete_user_id">
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone!
                        </div>
                        <p>Are you sure you want to delete user <strong id="delete_username"></strong>?</p>
                        <p class="text-muted">This will permanently remove the user from the system.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Edit User
        function editUser(userData) {
            document.getElementById('edit_user_id').value = userData.id;
            document.getElementById('edit_username').value = userData.username;
            document.getElementById('edit_email').value = userData.email;
            document.getElementById('edit_first_name').value = userData.first_name;
            document.getElementById('edit_last_name').value = userData.last_name;
            document.getElementById('edit_role').value = userData.role;
            document.getElementById('edit_phone').value = userData.phone || '';
            
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }

        // Change Password
        function changePassword(userId, username) {
            document.getElementById('password_user_id').value = userId;
            document.getElementById('password_username').value = username;
            document.getElementById('new_password').value = '';
            
            new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
        }

        // Delete User
        function deleteUser(userId, username) {
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_username').textContent = username;
            
            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }

        // Generate Password
        function generatePassword(inputId) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            document.getElementById(inputId).value = password;
            
            // Show success feedback
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-success"></i>';
            setTimeout(() => {
                button.innerHTML = originalHTML;
            }, 1000);
        }

        // Toggle Password Visibility
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + '_icon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Export Users
        function exportUsers() {
            alert('Export functionality will be implemented soon!');
        }

        // Reset All Passwords
        function resetAllPasswords() {
            if (confirm('Reset all user passwords to default (password123)? This will affect all users except yourself.')) {
                // This would require a separate endpoint to handle bulk password reset
                alert('Bulk password reset functionality will be implemented soon!');
            }
        }

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
