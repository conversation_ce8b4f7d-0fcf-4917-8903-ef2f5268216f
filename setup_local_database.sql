-- Local Database Setup for Expense Management System
-- Run this script to create the database and tables for localhost development

-- Create database
CREATE DATABASE IF NOT EXISTS expense_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE expense_management;

-- Create branches table
CREATE TABLE IF NOT EXISTS branches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(200) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    branch_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    type ENUM('income', 'expense') NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
);

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    branch_id INT NOT NULL,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(200),
    phone VARCHAR(20),
    address TEXT,
    admission_date DATE NOT NULL,
    status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
);

-- Create enquiries table
CREATE TABLE IF NOT EXISTS enquiries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    branch_id INT NOT NULL,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(200),
    phone VARCHAR(20),
    message TEXT,
    status ENUM('new', 'contacted', 'converted', 'closed') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
);

-- Create attendance table
CREATE TABLE IF NOT EXISTS attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    date DATE NOT NULL,
    status ENUM('present', 'absent', 'late') DEFAULT 'present',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'branch_manager', 'branch_staff') DEFAULT 'branch_staff',
    branch_id INT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL
);

-- Insert sample branches
INSERT INTO branches (name, location) VALUES
('Uppala', 'Uppala, Kerala'),
('Seethangoli', 'Seethangoli, Kerala'),
('Kasaragod', 'Kasaragod, Kerala'),
('Kochi', 'Kochi, Kerala');

-- Insert sample transactions
INSERT INTO transactions (branch_id, amount, type, description, category, date) VALUES
(1, 5000.00, 'expense', 'Office supplies and stationery', 'Office Supplies', '2024-01-15'),
(1, 2500.00, 'expense', 'Electricity bill payment', 'Utilities', '2024-01-16'),
(2, 3000.00, 'expense', 'Internet connection setup', 'Utilities', '2024-01-17'),
(2, 1500.00, 'expense', 'Cleaning services', 'Maintenance', '2024-01-18'),
(3, 4000.00, 'expense', 'Marketing materials printing', 'Marketing', '2024-01-19'),
(3, 2000.00, 'expense', 'Computer maintenance', 'Maintenance', '2024-01-20'),
(4, 6000.00, 'expense', 'Furniture purchase', 'Office Supplies', '2024-01-21'),
(4, 1800.00, 'expense', 'Water bill payment', 'Utilities', '2024-01-22'),
(1, 15000.00, 'income', 'Monthly fees collection', 'Fees', '2024-01-23'),
(2, 8000.00, 'income', 'Special training fees', 'Special Training Fees', '2024-01-24'),
(3, 12000.00, 'income', 'School training fees', 'School Training', '2024-01-25'),
(4, 6000.00, 'income', 'Community training fees', 'Community Training', '2024-01-26');

-- Insert sample students
INSERT INTO students (branch_id, name, email, phone, admission_date) VALUES
(1, 'John Doe', '<EMAIL>', '9876543210', '2024-01-01'),
(1, 'Jane Smith', '<EMAIL>', '9876543211', '2024-01-02'),
(2, 'Mike Johnson', '<EMAIL>', '9876543212', '2024-01-03'),
(2, 'Sarah Wilson', '<EMAIL>', '9876543213', '2024-01-04'),
(3, 'David Brown', '<EMAIL>', '9876543214', '2024-01-05'),
(3, 'Lisa Davis', '<EMAIL>', '9876543215', '2024-01-06'),
(4, 'Tom Miller', '<EMAIL>', '9876543216', '2024-01-07'),
(4, 'Emma Garcia', '<EMAIL>', '9876543217', '2024-01-08');

-- Insert sample enquiries
INSERT INTO enquiries (branch_id, name, email, phone, message, status) VALUES
(1, 'Alice Johnson', '<EMAIL>', '9876543220', 'Interested in admission', 'new'),
(2, 'Bob Smith', '<EMAIL>', '9876543221', 'Want to know about courses', 'contacted'),
(3, 'Carol Brown', '<EMAIL>', '9876543222', 'Fee structure enquiry', 'new'),
(4, 'Daniel Wilson', '<EMAIL>', '9876543223', 'Course duration details', 'converted');

-- Insert sample attendance
INSERT INTO attendance (student_id, date, status) VALUES
(1, '2024-01-15', 'present'),
(1, '2024-01-16', 'present'),
(2, '2024-01-15', 'present'),
(2, '2024-01-16', 'absent'),
(3, '2024-01-15', 'present'),
(3, '2024-01-16', 'late'),
(4, '2024-01-15', 'present'),
(4, '2024-01-16', 'present');

-- Insert sample users (passwords are 'password123' hashed)
INSERT INTO users (username, email, password, role, branch_id, first_name, last_name, phone, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NULL, 'System', 'Administrator', '9876543200', 'active'),
('uppala_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_manager', 1, 'Rajesh', 'Kumar', '9876543201', 'active'),
('uppala_staff', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_staff', 1, 'Priya', 'Sharma', '9876543202', 'active'),
('seethangoli_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_manager', 2, 'Suresh', 'Menon', '9876543203', 'active'),
('seethangoli_staff', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_staff', 2, 'Anita', 'Nair', '9876543204', 'active'),
('kasaragod_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_manager', 3, 'Vijay', 'Krishnan', '9876543205', 'active'),
('kasaragod_staff', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_staff', 3, 'Deepa', 'Pillai', '9876543206', 'active'),
('kochi_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_manager', 4, 'Ravi', 'Iyer', '9876543207', 'active'),
('kochi_staff', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch_staff', 4, 'Lakshmi', 'Reddy', '9876543208', 'active');

-- Create indexes for better performance
CREATE INDEX idx_transactions_date ON transactions(date);
CREATE INDEX idx_transactions_branch_id ON transactions(branch_id);
CREATE INDEX idx_students_branch_id ON students(branch_id);
CREATE INDEX idx_enquiries_branch_id ON enquiries(branch_id);
CREATE INDEX idx_attendance_student_id ON attendance(student_id);
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_users_branch_id ON users(branch_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
