<?php
// Complete Database Setup Script
// This script will create the database and all required tables

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'expense_management';

echo "<h2>Database Setup Script</h2>";
echo "<p>Setting up database: <strong>$database</strong></p>";

try {
    // First, connect without specifying database to create it
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ Connected to MySQL server</p>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database`");
    echo "<p>✓ Database '$database' created/verified</p>";
    
    // Select the database
    $pdo->exec("USE `$database`");
    echo "<p>✓ Using database '$database'</p>";
    
    // Create branches table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS branches (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(255) NOT NULL,
            manager_name VARCHAR(100),
            contact_phone VARCHAR(20),
            contact_email VARCHAR(100),
            address TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p>✓ Branches table created</p>";
    
    // Create transactions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            branch_id INT NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            type ENUM('income', 'expense') NOT NULL,
            description TEXT NOT NULL,
            category VARCHAR(100) NOT NULL,
            date DATE NOT NULL,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✓ Transactions table created</p>";
    
    // Create students table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS students (
            id INT AUTO_INCREMENT PRIMARY KEY,
            branch_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            age INT,
            grade VARCHAR(50),
            parent_name VARCHAR(100),
            parent_phone VARCHAR(20),
            parent_email VARCHAR(100),
            address TEXT,
            enrollment_date DATE NOT NULL,
            status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✓ Students table created</p>";
    
    // Create enquiries table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS enquiries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            branch_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(100),
            message TEXT,
            status ENUM('new', 'contacted', 'converted', 'closed') DEFAULT 'new',
            enquiry_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✓ Enquiries table created</p>";
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'branch_manager', 'branch_staff') DEFAULT 'branch_staff',
            branch_id INT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL
        )
    ");
    echo "<p>✓ Users table created</p>";
    
    // Insert sample branches
    $pdo->exec("
        INSERT IGNORE INTO branches (id, name, location, manager_name, contact_phone, contact_email, address, status) VALUES
        (1, 'Uppala', 'Uppala, Kasaragod', 'John Manager', '9876543210', '<EMAIL>', 'Main Street, Uppala', 'active'),
        (2, 'Seethangoli', 'Seethangoli, Kasaragod', 'Jane Manager', '9876543211', '<EMAIL>', 'Center Road, Seethangoli', 'active'),
        (3, 'Kasaragod', 'Kasaragod City', 'Mike Manager', '9876543212', '<EMAIL>', 'City Center, Kasaragod', 'active'),
        (4, 'Kochi', 'Kochi City', 'Sarah Manager', '9876543213', '<EMAIL>', 'Marine Drive, Kochi', 'active')
    ");
    echo "<p>✓ Sample branches inserted</p>";
    
    // Insert sample users
    $pdo->exec("
        INSERT IGNORE INTO users (id, username, email, password, role, branch_id, first_name, last_name, phone, status) VALUES
        (1, 'admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', 'admin', NULL, 'Admin', 'User', '9999999999', 'active'),
        (2, 'uppala_manager', '<EMAIL>', '" . password_hash('password123', PASSWORD_DEFAULT) . "', 'branch_manager', 1, 'John', 'Manager', '9876543210', 'active'),
        (3, 'seethangoli_manager', '<EMAIL>', '" . password_hash('password123', PASSWORD_DEFAULT) . "', 'branch_manager', 2, 'Jane', 'Manager', '9876543211', 'active'),
        (4, 'kasaragod_manager', '<EMAIL>', '" . password_hash('password123', PASSWORD_DEFAULT) . "', 'branch_manager', 3, 'Mike', 'Manager', '9876543212', 'active'),
        (5, 'kochi_manager', '<EMAIL>', '" . password_hash('password123', PASSWORD_DEFAULT) . "', 'branch_manager', 4, 'Sarah', 'Manager', '9876543213', 'active')
    ");
    echo "<p>✓ Sample users inserted</p>";
    
    // Insert sample transactions
    $pdo->exec("
        INSERT IGNORE INTO transactions (id, branch_id, amount, type, description, category, date, created_by) VALUES
        (1, 1, 5000.00, 'expense', 'Monthly rent payment', 'Rent', '2024-01-20', 1),
        (2, 2, 3000.00, 'expense', 'Water bill payment', 'Water', '2024-01-21', 1),
        (3, 3, 8000.00, 'expense', 'Equipment purchase', 'Equipment Purchases', '2024-01-22', 1),
        (4, 4, 2000.00, 'expense', 'Stationery supplies', 'Stationery', '2024-01-23', 1),
        (5, 1, 15000.00, 'income', 'Monthly fees collection', 'Fees', '2024-01-23', 1),
        (6, 2, 8000.00, 'income', 'Special training fees', 'Special Training Fees', '2024-01-24', 1),
        (7, 3, 12000.00, 'income', 'School training fees', 'School Training', '2024-01-25', 1),
        (8, 4, 6000.00, 'income', 'Community training fees', 'Community Training', '2024-01-26', 1),
        (9, 1, 4500.00, 'expense', 'Staff salary payment', 'Salary', '2024-01-27', 1),
        (10, 2, 3200.00, 'expense', 'Monthly utilities', 'Other', '2024-01-28', 1)
    ");
    echo "<p>✓ Sample transactions inserted</p>";
    
    // Insert sample students
    $pdo->exec("
        INSERT IGNORE INTO students (id, branch_id, name, age, grade, parent_name, parent_phone, parent_email, address, enrollment_date, status) VALUES
        (1, 1, 'Alice Johnson', 15, 'Grade 10', 'Robert Johnson', '9876543210', '<EMAIL>', '123 Main St, Uppala', '2024-01-15', 'active'),
        (2, 2, 'Bob Smith', 16, 'Grade 11', 'Mary Smith', '9876543211', '<EMAIL>', '456 Center Rd, Seethangoli', '2024-01-16', 'active'),
        (3, 3, 'Charlie Brown', 14, 'Grade 9', 'Lucy Brown', '9876543212', '<EMAIL>', '789 City St, Kasaragod', '2024-01-17', 'active'),
        (4, 4, 'Diana Prince', 17, 'Grade 12', 'Steve Prince', '9876543213', '<EMAIL>', '321 Marine Dr, Kochi', '2024-01-18', 'active')
    ");
    echo "<p>✓ Sample students inserted</p>";
    
    // Insert sample enquiries
    $pdo->exec("
        INSERT IGNORE INTO enquiries (id, branch_id, name, phone, email, message, status, enquiry_date) VALUES
        (1, 1, 'Emma Wilson', '9876543220', '<EMAIL>', 'Interested in enrollment for Grade 10', 'new', '2024-01-20'),
        (2, 2, 'Frank Miller', '9876543221', '<EMAIL>', 'Need information about special training programs', 'contacted', '2024-01-21'),
        (3, 3, 'Grace Lee', '9876543222', '<EMAIL>', 'Community training inquiry', 'new', '2024-01-22'),
        (4, 4, 'Henry Davis', '9876543223', '<EMAIL>', 'School training program details', 'converted', '2024-01-23')
    ");
    echo "<p>✓ Sample enquiries inserted</p>";
    
    echo "<h3 style='color: green;'>🎉 Database setup completed successfully!</h3>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Login Credentials:</h4>";
    echo "<p><strong>Admin:</strong> username: admin, password: admin123</p>";
    echo "<p><strong>Branch Managers:</strong> username: [branch]_manager, password: password123</p>";
    echo "<p>Example: uppala_manager, seethangoli_manager, etc.</p>";
    echo "</div>";
    echo "<p><a href='index.php' style='background: #22c55e; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ Database setup failed:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<h4>Troubleshooting:</h4>";
    echo "<ul>";
    echo "<li>Make sure MySQL is running on your system</li>";
    echo "<li>Check if the username 'root' and password are correct</li>";
    echo "<li>Ensure you have permission to create databases</li>";
    echo "<li>Try running: <code>mysql -u root -p</code> in terminal to test connection</li>";
    echo "</ul>";
}
?>


