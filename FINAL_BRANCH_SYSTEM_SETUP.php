<?php
require_once 'classes/User.php';
require_once 'classes/Branch.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Final Branch System Setup</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
    .feature-card { transition: transform 0.3s ease; }
    .feature-card:hover { transform: translateY(-5px); }
    .step-number { width: 40px; height: 40px; background: #22c55e; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; }
</style>";
echo "</head><body style='background: #f8f9fa;'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-lg-12'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-gradient text-white' style='background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);'>";
echo "<h1 class='mb-0 text-center'><i class='fas fa-building me-3'></i>Complete Branch Management System</h1>";
echo "<p class='text-center mb-0 mt-2'>Every branch has its own login system with full user management capabilities</p>";
echo "</div>";
echo "<div class='card-body p-4'>";

try {
    $user = new User();
    $branch = new Branch();
    
    // Check system status
    echo "<div class='row mb-4'>";
    echo "<div class='col-md-4'>";
    echo "<div class='card feature-card h-100'>";
    echo "<div class='card-body text-center'>";
    echo "<i class='fas fa-database fa-3x text-primary mb-3'></i>";
    echo "<h5>Database Status</h5>";
    
    $branches = $branch->getAllBranches();
    if (empty($branches)) {
        echo "<span class='badge bg-danger'>Not Set Up</span>";
        echo "<p class='text-muted mt-2'>Run setup_database.php first</p>";
    } else {
        echo "<span class='badge bg-success'>Ready</span>";
        echo "<p class='text-success mt-2'>" . count($branches) . " branches configured</p>";
    }
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card feature-card h-100'>";
    echo "<div class='card-body text-center'>";
    echo "<i class='fas fa-user-shield fa-3x text-warning mb-3'></i>";
    echo "<h5>Admin User</h5>";
    
    $adminUser = $user->getUserByUsername('admin');
    if (!$adminUser) {
        echo "<span class='badge bg-danger'>Missing</span>";
        echo "<p class='text-muted mt-2'>Admin user not found</p>";
    } else {
        echo "<span class='badge bg-success'>Ready</span>";
        echo "<p class='text-success mt-2'>Admin: admin / admin123</p>";
    }
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card feature-card h-100'>";
    echo "<div class='card-body text-center'>";
    echo "<i class='fas fa-users fa-3x text-info mb-3'></i>";
    echo "<h5>Branch Users</h5>";
    
    $allUsers = $user->getAllUsers();
    $branchUsers = array_filter($allUsers, function($u) { return $u['role'] !== 'admin'; });
    
    if (count($branchUsers) < 6) { // Less than 2 users per branch (4 branches)
        echo "<span class='badge bg-warning'>Incomplete</span>";
        echo "<p class='text-muted mt-2'>" . count($branchUsers) . " branch users</p>";
    } else {
        echo "<span class='badge bg-success'>Ready</span>";
        echo "<p class='text-success mt-2'>" . count($branchUsers) . " branch users</p>";
    }
    echo "</div></div></div>";
    echo "</div>";
    
    // Setup steps
    echo "<div class='row'>";
    echo "<div class='col-lg-8'>";
    echo "<h3 class='mb-4'><i class='fas fa-list-ol me-2'></i>System Features</h3>";
    
    $features = [
        [
            'icon' => 'fas fa-sign-in-alt',
            'title' => 'Branch-Specific Login',
            'description' => 'Each branch has its own login system with automatic redirection',
            'color' => 'primary'
        ],
        [
            'icon' => 'fas fa-users',
            'title' => 'User Management',
            'description' => 'Create, edit, delete, and manage users for each branch',
            'color' => 'success'
        ],
        [
            'icon' => 'fas fa-key',
            'title' => 'Password Management',
            'description' => 'Reset passwords, generate secure passwords, manage access',
            'color' => 'warning'
        ],
        [
            'icon' => 'fas fa-building',
            'title' => 'Branch Dashboards',
            'description' => 'Individual dashboards for each branch with user management',
            'color' => 'info'
        ],
        [
            'icon' => 'fas fa-shield-alt',
            'title' => 'Role-Based Access',
            'description' => 'Branch managers can manage users, staff have limited access',
            'color' => 'danger'
        ],
        [
            'icon' => 'fas fa-chart-bar',
            'title' => 'User Statistics',
            'description' => 'View user counts, roles, and branch-specific analytics',
            'color' => 'secondary'
        ]
    ];
    
    foreach ($features as $index => $feature) {
        echo "<div class='card mb-3'>";
        echo "<div class='card-body'>";
        echo "<div class='d-flex align-items-center'>";
        echo "<div class='step-number me-3'>" . ($index + 1) . "</div>";
        echo "<i class='{$feature['icon']} fa-2x text-{$feature['color']} me-3'></i>";
        echo "<div>";
        echo "<h6 class='mb-1'>{$feature['title']}</h6>";
        echo "<p class='text-muted mb-0'>{$feature['description']}</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Login credentials
    echo "<div class='col-lg-4'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5 class='mb-0'><i class='fas fa-key me-2'></i>Login Credentials</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<h6 class='text-danger'>Admin Access:</h6>";
    echo "<div class='bg-light p-3 rounded mb-3'>";
    echo "<strong>URL:</strong> <a href='login.php' target='_blank'>login.php</a><br>";
    echo "<strong>Username:</strong> admin<br>";
    echo "<strong>Password:</strong> admin123<br>";
    echo "<strong>Access:</strong> Full system management";
    echo "</div>";
    
    echo "<h6 class='text-success'>Branch Access:</h6>";
    echo "<div class='bg-light p-3 rounded mb-3'>";
    echo "<strong>URL:</strong> <a href='branch_login.php' target='_blank'>branch_login.php</a><br>";
    echo "<strong>Process:</strong> Select branch → Login<br>";
    echo "<strong>Format:</strong> [branch]_manager / [branch]_staff<br>";
    echo "<strong>Password:</strong> password123";
    echo "</div>";
    
    // Branch-specific credentials
    if (!empty($branches)) {
        echo "<h6 class='text-info'>Branch Examples:</h6>";
        echo "<div class='bg-light p-3 rounded'>";
        foreach (array_slice($branches, 0, 2) as $branch) {
            $clean_name = strtolower(str_replace(' ', '', $branch['name']));
            echo "<strong>{$branch['name']}:</strong><br>";
            echo "• Manager: {$clean_name}_manager<br>";
            echo "• Staff: {$clean_name}_staff<br>";
            echo "<br>";
        }
        echo "<small class='text-muted'>... and more branches</small>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Action buttons
    echo "<div class='row mt-4'>";
    echo "<div class='col-12'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'><i class='fas fa-rocket me-2'></i>Quick Actions</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<div class='row text-center'>";
    
    $actions = [
        ['url' => 'complete_branch_setup.php', 'icon' => 'fas fa-cog', 'text' => 'Complete Setup', 'color' => 'primary'],
        ['url' => 'branch_login.php', 'icon' => 'fas fa-building', 'text' => 'Branch Login', 'color' => 'success'],
        ['url' => 'login.php', 'icon' => 'fas fa-user-shield', 'text' => 'Admin Login', 'color' => 'warning'],
        ['url' => 'setup_branch_users.php', 'icon' => 'fas fa-users', 'text' => 'Create Users', 'color' => 'info'],
        ['url' => 'branch_user_management.php', 'icon' => 'fas fa-user-cog', 'text' => 'User Management', 'color' => 'danger'],
        ['url' => 'index.php', 'icon' => 'fas fa-tachometer-alt', 'text' => 'Dashboard', 'color' => 'secondary']
    ];
    
    foreach ($actions as $action) {
        echo "<div class='col-md-4 col-6 mb-3'>";
        echo "<a href='{$action['url']}' class='btn btn-{$action['color']} w-100' target='_blank'>";
        echo "<i class='{$action['icon']} me-2'></i>{$action['text']}";
        echo "</a>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // System summary
    if (!empty($branches)) {
        echo "<div class='row mt-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h5 class='mb-0'><i class='fas fa-info-circle me-2'></i>System Summary</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        echo "<div class='row text-center'>";
        echo "<div class='col-md-3'>";
        echo "<div class='border rounded p-3'>";
        echo "<h3 class='text-primary'>" . count($branches) . "</h3>";
        echo "<small class='text-muted'>Branches</small>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='border rounded p-3'>";
        echo "<h3 class='text-success'>" . count($allUsers) . "</h3>";
        echo "<small class='text-muted'>Total Users</small>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='border rounded p-3'>";
        echo "<h3 class='text-warning'>" . count(array_filter($allUsers, function($u) { return $u['role'] === 'branch_manager'; })) . "</h3>";
        echo "<small class='text-muted'>Managers</small>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='border rounded p-3'>";
        echo "<h3 class='text-info'>" . count(array_filter($allUsers, function($u) { return $u['role'] === 'branch_staff'; })) . "</h3>";
        echo "<small class='text-muted'>Staff</small>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ System Check Failed</h5>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try running <a href='setup_database.php'>setup_database.php</a> first.</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</body></html>";
?>
